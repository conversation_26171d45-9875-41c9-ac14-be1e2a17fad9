{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\systemSetting\\administratorAuthority\\permissionRules\\index.vue", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\systemSetting\\administratorAuthority\\permissionRules\\index.vue", "mtime": 1754373403829}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\eslint-loader\\index.js", "mtime": 1754052677050}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=bac4044c\"\nimport script from \"./index.vue?vue&type=script&lang=js\"\nexport * from \"./index.vue?vue&type=script&lang=js\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=bac4044c&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹 (3)\\\\adminUI\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('bac4044c')) {\n      api.createRecord('bac4044c', component.options)\n    } else {\n      api.reload('bac4044c', component.options)\n    }\n    module.hot.accept(\"./index.vue?vue&type=template&id=bac4044c\", function () {\n      api.rerender('bac4044c', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/systemSetting/administratorAuthority/permissionRules/index.vue\"\nexport default component.exports"]}
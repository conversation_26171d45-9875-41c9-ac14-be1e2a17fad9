{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\utils\\request.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\utils\\request.js", "mtime": 1754384004733}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754052678844}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\eslint-loader\\index.js", "mtime": 1754052677050}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _axios = _interopRequireDefault(require(\"axios\"));\nvar _elementUi = require(\"element-ui\");\nvar _store = _interopRequireDefault(require(\"@/store\"));\nvar _auth = require(\"@/utils/auth\");\nvar _settingMer = _interopRequireDefault(require(\"@/utils/settingMer\"));\nvar _wechat = require(\"@/libs/wechat\");\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\n// +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2021 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\n\nvar service = _axios.default.create({\n  baseURL: _settingMer.default.apiBaseURL,\n  timeout: 60000 // 过期时间\n});\n\n// request interceptor\nservice.interceptors.request.use(function (config) {\n  // 发送请求之前做的\n  var token = !_store.default.getters.token ? sessionStorage.getItem('token') : _store.default.getters.token;\n  if (token) {\n    config.headers['Authori-zation'] = token;\n  }\n  if (/get/i.test(config.method)) {\n    config.params = config.params || {};\n    config.params.temp = Date.parse(new Date()) / 1000;\n  }\n  return config;\n}, function (error) {\n  return Promise.reject(error);\n});\n\n// response interceptor\nservice.interceptors.response.use(function (response) {\n  var res = response.data;\n  // if the custom code is not 20000, it is judged as an error.\n  if (res.code === 401) {\n    // to re-login\n    _elementUi.Message.error('无效的会话，或者登录已过期，请重新登录。');\n    location.href = '/login';\n  } else if (res.code === 403) {\n    _elementUi.Message.error('没有权限访问。');\n  }\n  if (res.code !== 200 && res.code !== 401) {\n    if ((0, _wechat.isPhone)()) {\n      //移动端\n      return Promise.reject(res || 'Error');\n    }\n    (0, _elementUi.Message)({\n      message: res.message || 'Error',\n      type: 'error',\n      duration: 5 * 1000\n    });\n    return Promise.reject();\n  } else {\n    return res.data;\n  }\n}, function (error) {\n  (0, _elementUi.Message)({\n    message: error.message,\n    type: 'error',\n    duration: 5 * 1000\n  });\n  return Promise.reject(error);\n});\nvar _default = exports.default = service;", null]}
{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\layout\\components\\TagsView\\index.vue?vue&type=template&id=fac8ca64&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\layout\\components\\TagsView\\index.vue", "mtime": 1754375474832}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754052682065}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["\n<div id=\"tags-view-container\" class=\"tags-view-container\" v-if=\"!isPhone\">\n  <scroll-pane ref=\"scrollPane\" class=\"tags-view-wrapper\">\n    <router-link\n      v-for=\"tag in visitedViews\"\n      :key=\"tag.path\"\n      :class=\"isActive(tag) ? 'active' : ''\"\n      :to=\"{ path: tag.path, query: tag.query, fullPath: tag.fullPath }\"\n      tag=\"span\"\n      class=\"tags-view-item\"\n      @click.middle.native=\"!isAffix(tag) ? closeSelectedTag(tag) : ''\"\n      @contextmenu.prevent.native=\"openMenu(tag, $event)\"\n    >\n      {{ getTagTitle(tag) }}\n      <span\n        v-if=\"!isAffix(tag)\"\n        class=\"el-icon-close\"\n        @click.prevent.stop=\"closeSelectedTag(tag)\"\n      />\n    </router-link>\n  </scroll-pane>\n  <ul\n    v-show=\"visible\"\n    :style=\"{ left: left + 'px', top: top + 'px' }\"\n    class=\"contextmenu\"\n  >\n    <li @click=\"refreshSelectedTag(selectedTag)\">\n      {{ $t(\"tagsView.refresh\") }}\n    </li>\n    <li v-if=\"!isAffix(selectedTag)\" @click=\"closeSelectedTag(selectedTag)\">\n      {{ $t(\"tagsView.close\") }}\n    </li>\n    <li @click=\"closeOthersTags\">{{ $t(\"tagsView.closeOthers\") }}</li>\n    <li @click=\"closeAllTags(selectedTag)\">{{ $t(\"tagsView.closeAll\") }}</li>\n  </ul>\n</div>\n", null]}
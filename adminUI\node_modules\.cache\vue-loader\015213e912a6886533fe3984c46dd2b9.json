{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\components\\Breadcrumb\\index.vue?vue&type=template&id=b50ef614&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\components\\Breadcrumb\\index.vue", "mtime": 1754380923819}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754052682065}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["\n<el-breadcrumb class=\"app-breadcrumb\" separator=\"/\">\n  <transition-group name=\"breadcrumb\">\n    <el-breadcrumb-item v-for=\"(item,index) in levelList\" :key=\"item.path\">\n      <span v-if=\"item.redirect==='noRedirect'||index==levelList.length-1\" class=\"no-redirect\">{{ $t('dashboard.'+item.meta.title) }}</span>\n      <a v-else @click.prevent=\"handleLink(item)\">{{ $t('dashboard.'+item.meta.title) }}</a>\n    </el-breadcrumb-item>\n  </transition-group>\n</el-breadcrumb>\n", null]}
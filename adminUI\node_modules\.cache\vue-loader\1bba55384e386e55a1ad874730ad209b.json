{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\financial\\detail\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\financial\\detail\\index.vue", "mtime": 1754368689718}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754052678844}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport {\r\n  topUpLogListsApi,\r\n  fundsMonitorListApi,\r\n  extractBankApi\r\n} from \"@/api/financial\";\r\nexport default {\r\n  name: \"FinancialDetail\",\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      purchaseTableData: [],\r\n      tradeTableData: [],\r\n      timeList: [],\r\n      purchaseFrom: {\r\n        uid: \"\",\r\n        keyword: \"\",\r\n        rechargeType: \"\",\r\n        dateLimit: [],\r\n        payChannel: \"\",\r\n        walletCode: \"\",\r\n        bankName: \"\",\r\n        page: 1,\r\n        limit: 20,\r\n        total: 0\r\n      },\r\n      tradeFrom: {\r\n        keyword: \"\",\r\n        dateLimit: [],\r\n        linkId: \"\",\r\n        type: \"\",\r\n        page: 1,\r\n        limit: 20,\r\n        total: 0\r\n      },\r\n      tableFromType: \"purchase\",\r\n      rechargeTypeList: [\r\n        { label: \"agentFee\", value: \"agent\" },\r\n        { label: \"partnerFee\", value: \"partner\" }\r\n      ],\r\n      walletList: [\r\n        { label: \"ShopeePay\", value: \"ShopeePay\" },\r\n        { label: \"DANA\", value: \"DANA\" },\r\n        { label: \"OVO\", value: \"OVO\" },\r\n        { label: \"Gopay\", value: \"Gopay\" }\r\n      ],\r\n      bankList: []\r\n    };\r\n  },\r\n  created() {},\r\n  mounted() {\r\n    this.getList(this.tableFromType);\r\n    this.getBankList();\r\n  },\r\n  methods: {\r\n    // 获取银行列表\r\n    getBankList() {\r\n      extractBankApi()\r\n        .then(res => {\r\n          this.bankList = res;\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    getList(type, num) {\r\n      this.loading = true;\r\n      if (type === \"purchase\") {\r\n        this.purchaseFrom.page = num ? num : this.purchaseFrom.page;\r\n        this.purchaseFrom.dateLimit = this.timeList.length\r\n          ? this.timeList.join(\",\")\r\n          : \"\";\r\n        topUpLogListsApi(this.purchaseFrom).then(res => {\r\n          this.purchaseTableData = res.list;\r\n          this.purchaseFrom.total = res.total;\r\n          this.loading = false;\r\n        });\r\n      } else {\r\n        this.tradeFrom.page = num ? num : this.tradeFrom.page;\r\n        this.tradeFrom.dateLimit = this.timeList.length\r\n          ? this.timeList.join(\",\")\r\n          : \"\";\r\n        fundsMonitorListApi(this.tradeFrom).then(res => {\r\n          this.tradeTableData = res.list;\r\n          this.tradeFrom.total = res.total;\r\n          this.loading = false;\r\n        });\r\n      }\r\n    },\r\n    //切换页数\r\n    pageChange(index, type) {\r\n      if (type === \"purchase\") {\r\n        this.purchaseFrom.page = index;\r\n      } else {\r\n        this.tradeFrom.page = index;\r\n      }\r\n      this.getList(type);\r\n    },\r\n    //切换显示条数\r\n    sizeChange(index, type) {\r\n      if (type === \"purchase\") {\r\n        this.purchaseFrom.limit = index;\r\n      } else {\r\n        this.tradeFrom.limit = index;\r\n      }\r\n      this.getList(type);\r\n    },\r\n    handleReset() {\r\n      this.purchaseFrom = {\r\n        uid: \"\",\r\n        keyword: \"\",\r\n        rechargeType: \"\",\r\n        dateLimit: [],\r\n        payChannel: \"\",\r\n        walletCode: \"\",\r\n        bankName: \"\",\r\n        page: 1,\r\n        limit: 20,\r\n        total: 0\r\n      };\r\n      this.tradeFrom = {\r\n        keyword: \"\",\r\n        dateLimit: [],\r\n        linkId: \"\",\r\n        type: \"\",\r\n        page: 1,\r\n        limit: 20,\r\n        total: 0\r\n      };\r\n    },\r\n    handleUpload() {}\r\n  }\r\n};\r\n", null]}
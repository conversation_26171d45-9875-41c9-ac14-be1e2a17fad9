{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\router\\modules\\user.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\router\\modules\\user.js", "mtime": 1754381563690}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754052678844}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\eslint-loader\\index.js", "mtime": 1754052677050}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _layout = _interopRequireDefault(require(\"@/layout\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != _typeof(e) && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); } // +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2021 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\nvar userRouter = {\n  path: '/user',\n  component: _layout.default,\n  redirect: '/user/index',\n  name: 'User',\n  meta: {\n    title: 'userCenter',\n    icon: 'clipboard'\n  },\n  children: [{\n    path: 'center',\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/user/center/index'));\n      });\n    },\n    name: 'UserCenter',\n    meta: {\n      title: 'userCenter',\n      icon: ''\n    }\n  }, {\n    path: 'index',\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/user/list/index'));\n      });\n    },\n    name: 'UserIndex',\n    meta: {\n      title: 'userManage',\n      icon: ''\n    }\n  }, {\n    path: 'grade',\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/user/grade/index'));\n      });\n    },\n    name: 'Grade',\n    meta: {\n      title: 'userLevel',\n      icon: ''\n    }\n  }, {\n    path: 'upgrade',\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/user/upgrade/index'));\n      });\n    },\n    name: 'UserLevelUpgrade',\n    meta: {\n      title: 'levelUpgradeOrder',\n      icon: ''\n    }\n  }, {\n    path: 'label',\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/user/group/index'));\n      });\n    },\n    name: 'Label',\n    meta: {\n      title: '用户标签',\n      icon: ''\n    }\n  }, {\n    path: 'group',\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/user/group/index'));\n      });\n    },\n    name: 'Group',\n    meta: {\n      title: '用户分组',\n      icon: ''\n    }\n  }]\n};\nvar _default = exports.default = userRouter;", null]}
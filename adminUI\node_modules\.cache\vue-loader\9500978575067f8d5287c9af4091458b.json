{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\order\\search.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\order\\search.vue", "mtime": 1754385359909}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754052678844}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport { orderListApi } from \"@/api/order\";\r\nexport default {\r\n  name: \"OrderSearch\",\r\n  data () {\r\n    return {\r\n      loading: false,\r\n      searchFrom: {\r\n        orderNo: \"\",\r\n        productTitle: \"\",\r\n        type: \"2\",\r\n        dateLimit: \"\",\r\n        page: 1,\r\n        limit: 20,\r\n        total: 0\r\n      },\r\n      tableData: [],\r\n      statusList: [\r\n        { value: \"\", label: \"all\" },\r\n        { value: \"unPaid\", label: \"unPaid\" },\r\n        { value: \"notShipped\", label: \"notShipped\" },\r\n        { value: \"spike\", label: \"spike\" },\r\n        { value: \"bargain\", label: \"bargain\" },\r\n        { value: \"complete\", label: \"complete\" },\r\n        { value: \"toBeWrittenOff\", label: \"toBeWrittenOff\" },\r\n        { value: \"refunding\", label: \"refunding\" },\r\n        { value: \"refunded\", label: \"refunded\" },\r\n        { value: \"deleted\", label: \"deleted\" }\r\n      ]\r\n    };\r\n  },\r\n  created () { },\r\n  mounted () {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    // 列表\r\n    getList (num) {\r\n        let _this = this;\r\n      this.loading = true;\r\n\r\n      this.searchFrom.page = num ? num : this.searchFrom.page;\r\n      orderListApi(this.searchFrom)\r\n        .then(res => {\r\n          this.tableData = res.list || [];\r\n          this.tableData.forEach(item => {\r\n            item.payCount = item.productList ? item.productList.length : 0;\r\n            // item.statusLabel = this.statusList.filter(\r\n            //   i => i.value == item.status\r\n            // )[0].label;\r\n\r\n            // 安全检查：确保productList存在且不为空\r\n            if (item.productList && item.productList.length > 0) {\r\n              item.productName = item.productList[0].productName;\r\n              item.actualCommission = item.productList[0].actualCommission;\r\n              item.commissionRate = item.productList[0].commissionRate;\r\n              item.contentId = item.productList[0].contentId;\r\n              item.estimatedCommission = item.productList[0].estimatedCommission;\r\n              item.price = _this.formatAmount(item.productList[0].price)\r\n              item.image = item.productList[0].image\r\n            } else {\r\n              // 设置默认值\r\n              item.productName = '';\r\n              item.actualCommission = 0;\r\n              item.commissionRate = 0;\r\n              item.contentId = '';\r\n              item.estimatedCommission = 0;\r\n              item.price = _this.formatAmount(0);\r\n              item.image = '';\r\n            }\r\n\r\n            item.totalPrice = _this.formatAmount(item.totalPrice)\r\n\r\n          });\r\n          this.searchFrom.total = res.total || 0;\r\n          this.loading = false;\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n          this.tableData = [];\r\n          this.searchFrom.total = 0;\r\n        });\r\n    },\r\n    formatAmount(s){\r\n        if(s == undefined) {\r\n            s = 0\r\n        }\r\n        let s1 = (s/1000).toFixed(3)\r\n        return s1\r\n    },\r\n    //切换页数\r\n    pageChange (index) {\r\n      this.searchFrom.page = index;\r\n      this.getList();\r\n    },\r\n    //切换显示条数\r\n    sizeChange (index) {\r\n      this.searchFrom.limit = index;\r\n      this.getList();\r\n    },\r\n    resetForm () {\r\n      this.searchFrom = {\r\n        orderNo: \"\",\r\n        productTitle: \"\",\r\n        type: \"2\",\r\n        dateLimit: \"\",\r\n        page: 1,\r\n        limit: 20,\r\n        total: 0\r\n      };\r\n      this.getList();\r\n    },\r\n    formatRate (s) {\r\n        if(s == undefined) {\r\n            s = 0\r\n        }\r\n         return parseInt(s * 10000) / 100 + \"%\";\r\n    }\r\n  }\r\n};\r\n", null]}
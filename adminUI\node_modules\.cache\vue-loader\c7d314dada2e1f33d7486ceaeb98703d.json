{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\operations\\affiliate-products\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\operations\\affiliate-products\\index.vue", "mtime": 1754374966345}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754052678844}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport { searchAffiliateProducts } from '@/api/tiktok'\r\nimport { importAffiliateProducts, deleteAffiliateProducts } from '@/api/affiliate-products'\r\n\r\nexport default {\r\n  name: 'AffiliateProducts',\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      tableData: [],\r\n      searchForm: {\r\n        keywords: [], // 改为数组存储多个关键词\r\n        priceMin: '',\r\n        priceMax: '',\r\n        commissionMin: '',\r\n        commissionMax: '',\r\n\r\n        sortField: 'commission_rate',\r\n        sortOrder: 'DESC',\r\n        pageSize: 10 // 默认分页数量改为10\r\n      },\r\n      searchRules: {\r\n        // 移除关键词必填验证\r\n      },\r\n      currentCursor: '',\r\n      nextPageToken: '',\r\n      prevPageTokens: [], // 存储前面页面的token，用于返回上一页\r\n      totalCount: 0,\r\n      hasNextPage: false,\r\n      hasPrevPage: false,\r\n      hasSearched: false, // 标记是否已经搜索过\r\n      selectedProducts: [], // 选中的商品列表\r\n      batchImporting: false, // 批量入库状态\r\n      batchDeleting: false, // 批量删除状态\r\n      importDialogVisible: false, // 入库确认对话框\r\n      currentImportProduct: null, // 当前导入的商品（单个导入时使用）\r\n      commissionErrors: { // 佣金率验证错误信息\r\n        commissionMin: '',\r\n        commissionMax: ''\r\n      },\r\n      currentKeyword: '', // 当前输入的关键词\r\n      keywordError: '', // 关键词验证错误信息\r\n\r\n    }\r\n  },\r\n  mounted() {\r\n    // 页面加载时不自动搜索，等待用户点击查询按钮\r\n  },\r\n  methods: {\r\n    // 佣金率验证\r\n    validateCommissionRate(field) {\r\n      const value = this.searchForm[field]\r\n      if (value === '' || value === null || value === undefined) {\r\n        this.commissionErrors[field] = ''\r\n        return true\r\n      }\r\n\r\n      const numValue = parseFloat(value)\r\n      if (isNaN(numValue)) {\r\n        this.commissionErrors[field] = this.$t('affiliateProducts.commissionInvalidNumber')\r\n        return false\r\n      }\r\n\r\n      if (numValue !== 0 && numValue < 1000) {\r\n        this.commissionErrors[field] = this.$t('affiliateProducts.commissionRangeError')\r\n        return false\r\n      }\r\n\r\n      this.commissionErrors[field] = ''\r\n      return true\r\n    },\r\n\r\n    // 清除佣金率错误信息\r\n    clearCommissionError(field) {\r\n      this.commissionErrors[field] = ''\r\n    },\r\n\r\n    // 添加关键词\r\n    addKeyword() {\r\n      const keyword = this.currentKeyword.trim()\r\n      if (!keyword) {\r\n        this.keywordError = ''\r\n        return\r\n      }\r\n\r\n      // 验证关键词数量限制\r\n      if (this.searchForm.keywords.length >= 20) {\r\n        this.keywordError = this.$t('affiliateProducts.keywordLimitExceeded')\r\n        return\r\n      }\r\n\r\n      // 验证关键词长度\r\n      if (keyword.length > 255) {\r\n        this.keywordError = this.$t('affiliateProducts.keywordTooLong')\r\n        return\r\n      }\r\n\r\n      // 检查是否重复\r\n      if (this.searchForm.keywords.includes(keyword)) {\r\n        this.keywordError = this.$t('affiliateProducts.keywordDuplicate')\r\n        return\r\n      }\r\n\r\n      // 添加关键词\r\n      this.searchForm.keywords.push(keyword)\r\n      this.currentKeyword = ''\r\n      this.keywordError = ''\r\n    },\r\n\r\n    // 移除关键词\r\n    removeKeyword(index) {\r\n      this.searchForm.keywords.splice(index, 1)\r\n      this.keywordError = ''\r\n    },\r\n\r\n    // 清理所有关键词\r\n    clearAllKeywords() {\r\n      this.$confirm(\r\n        this.$t('affiliateProducts.confirmClearAllKeywords'),\r\n        this.$t('affiliateProducts.clearAllKeywords'),\r\n        {\r\n          confirmButtonText: this.$t('common.confirm'),\r\n          cancelButtonText: this.$t('common.cancel'),\r\n          type: 'warning'\r\n        }\r\n      ).then(() => {\r\n        this.searchForm.keywords = []\r\n        this.keywordError = ''\r\n        this.$message.success(this.$t('affiliateProducts.keywordsClearedSuccess'))\r\n      }).catch(() => {\r\n        // 用户取消操作\r\n      })\r\n    },\r\n\r\n\r\n\r\n    // 搜索\r\n    handleSearch() {\r\n      // 验证佣金率字段\r\n      const isCommissionMinValid = this.validateCommissionRate('commissionMin')\r\n      const isCommissionMaxValid = this.validateCommissionRate('commissionMax')\r\n\r\n      if (!isCommissionMinValid || !isCommissionMaxValid) {\r\n        this.$message.error(this.$t('affiliateProducts.pleaseFixErrors'))\r\n        return\r\n      }\r\n\r\n      this.currentCursor = ''\r\n      this.nextPageToken = ''\r\n      this.prevPageTokens = []\r\n      this.hasPrevPage = false\r\n      this.hasSearched = true\r\n      this.loadData()\r\n    },\r\n\r\n    // 重置\r\n    handleReset() {\r\n      this.searchForm = {\r\n        keywords: [], // 重置为空数组\r\n        priceMin: '',\r\n        priceMax: '',\r\n        commissionMin: '',\r\n        commissionMax: '',\r\n\r\n        sortField: 'commission_rate',\r\n        sortOrder: 'DESC',\r\n        pageSize: 10 // 重置时也使用默认分页数量10\r\n      }\r\n      // 清除错误信息\r\n      this.commissionErrors = {\r\n        commissionMin: '',\r\n        commissionMax: ''\r\n      }\r\n      this.currentKeyword = ''\r\n      this.keywordError = ''\r\n      // 重置表单（无需验证状态重置）\r\n      // 清空表格数据\r\n      this.tableData = []\r\n      this.hasSearched = false\r\n      this.totalCount = 0\r\n      this.hasNextPage = false\r\n      this.hasPrevPage = false\r\n      this.currentCursor = ''\r\n      this.nextPageToken = ''\r\n      this.prevPageTokens = []\r\n    },\r\n\r\n    // 刷新\r\n    handleRefresh() {\r\n      if (this.hasSearched) {\r\n        this.loadData()\r\n      } else {\r\n        this.$message.warning(this.$t('affiliateProducts.searchFirst'))\r\n      }\r\n    },\r\n\r\n    // 下一页\r\n    handleNextPage() {\r\n      if (this.hasNextPage && this.nextPageToken) {\r\n        this.prevPageTokens.push(this.currentCursor)\r\n        this.currentCursor = this.nextPageToken\r\n        this.hasPrevPage = true\r\n        this.loadData()\r\n      }\r\n    },\r\n\r\n    // 上一页\r\n    handlePrevPage() {\r\n      if (this.hasPrevPage && this.prevPageTokens.length > 0) {\r\n        this.currentCursor = this.prevPageTokens.pop()\r\n        if (this.prevPageTokens.length === 0) {\r\n          this.hasPrevPage = false\r\n        }\r\n        this.loadData()\r\n      }\r\n    },\r\n\r\n    // 加载数据\r\n    loadData() {\r\n      this.loading = true\r\n\r\n      // 构建请求参数 - 与V202405 AffiliateProductSearchRequest保持完全一致\r\n      const params = {\r\n        // 基础分页和排序参数\r\n        pageSize: this.searchForm.pageSize,\r\n        cursor: this.currentCursor,\r\n        sortField: this.searchForm.sortField,\r\n        sortOrder: this.searchForm.sortOrder,\r\n\r\n        // 搜索过滤参数（按V202405 API字段名）\r\n        titleKeywords: this.searchForm.keywords.length > 0 ? this.searchForm.keywords : null,\r\n        salesPriceMin: this.searchForm.priceMin ? parseFloat(this.searchForm.priceMin) : null,\r\n        salesPriceMax: this.searchForm.priceMax ? parseFloat(this.searchForm.priceMax) : null,\r\n        commissionRateMin: this.searchForm.commissionMin ? parseFloat(this.searchForm.commissionMin) : null,\r\n        commissionRateMax: this.searchForm.commissionMax ? parseFloat(this.searchForm.commissionMax) : null,\r\n\r\n      }\r\n\r\n      searchAffiliateProducts(params)\r\n        .then(res => {\r\n          this.tableData = res.products || []\r\n          this.nextPageToken = res.nextPageToken || ''\r\n          this.totalCount = res.totalCount || 0\r\n          this.hasNextPage = !!this.nextPageToken\r\n\r\n          if (this.tableData.length === 0) {\r\n            this.$message.info(this.$t('affiliateProducts.noResults'))\r\n          }\r\n        })\r\n        .catch(() => {\r\n          this.loading = false\r\n        })\r\n        .finally(() => {\r\n          this.loading = false\r\n        })\r\n    },\r\n\r\n    // 格式化价格\r\n    formatPrice(priceObj) {\r\n      if (!priceObj) return '-'\r\n      const min = priceObj.minimumAmount\r\n      const max = priceObj.maximumAmount\r\n      const currency = priceObj.currency\r\n\r\n      if (min === max) {\r\n        return `${min} ${currency}`\r\n      } else {\r\n        return `${min} - ${max} ${currency}`\r\n      }\r\n    },\r\n\r\n    // 格式化佣金率（基点转百分比）\r\n    formatCommissionRate(rate) {\r\n      if (!rate) return '0'\r\n      return (rate / 100).toFixed(2)\r\n    },\r\n\r\n    // 处理选择变化\r\n    handleSelectionChange(selection) {\r\n      this.selectedProducts = selection\r\n    },\r\n\r\n    // 单个商品导入\r\n    handleImportProduct(product) {\r\n      // 设置当前操作的商品\r\n      this.currentImportProduct = product\r\n      this.importDialogVisible = true\r\n    },\r\n\r\n    // 单个商品删除\r\n    handleDeleteProduct(product) {\r\n      this.$confirm(this.$t('affiliateProducts.deleteConfirm'), this.$t('common.deleteConfirm'), {\r\n        confirmButtonText: this.$t('common.confirm'),\r\n        cancelButtonText: this.$t('common.cancel'),\r\n        type: 'warning'\r\n      })\r\n      .then(() => {\r\n        product.deleting = true\r\n\r\n        deleteAffiliateProducts([product.id])\r\n        .then(() => {\r\n          this.$message.success(this.$t('affiliateProducts.deleteSuccess'))\r\n        })\r\n        .catch(() => {\r\n          // 错误消息已在响应拦截器中处理\r\n        })\r\n        .finally(() => {\r\n          product.deleting = false\r\n        })\r\n      })\r\n      .catch(() => {\r\n        // 用户取消删除，不需要处理\r\n      })\r\n    },\r\n\r\n    // 批量导入\r\n    handleBatchImport() {\r\n      if (this.selectedProducts.length === 0) {\r\n        this.$message.warning(this.$t('affiliateProducts.selectFirst'))\r\n        return\r\n      }\r\n\r\n      // 设置批量导入模式\r\n      this.currentImportProduct = null\r\n      this.importDialogVisible = true\r\n    },\r\n\r\n    // 批量删除\r\n    handleBatchDelete() {\r\n      if (this.selectedProducts.length === 0) {\r\n        this.$message.warning(this.$t('affiliateProducts.selectDeleteFirst'))\r\n        return\r\n      }\r\n\r\n      this.$confirm(this.$t('affiliateProducts.batchDeleteConfirm', { count: this.selectedProducts.length }), this.$t('affiliateProducts.batchDelete'), {\r\n        confirmButtonText: this.$t('common.confirm'),\r\n        cancelButtonText: this.$t('common.cancel'),\r\n        type: 'warning'\r\n      })\r\n      .then(() => {\r\n        this.batchDeleting = true\r\n\r\n        const productIds = this.selectedProducts.map(p => p.id)\r\n        deleteAffiliateProducts(productIds)\r\n        .then(() => {\r\n          this.$message.success(this.$t('affiliateProducts.batchDeleteSuccess'))\r\n          // 清空选择\r\n          this.$refs.productTable.clearSelection()\r\n        })\r\n        .catch(() => {\r\n          // 错误消息已在响应拦截器中处理\r\n        })\r\n        .finally(() => {\r\n          this.batchDeleting = false\r\n        })\r\n      })\r\n      .catch(() => {\r\n        // 用户取消删除，不需要处理\r\n      })\r\n    },\r\n\r\n\r\n\r\n    // 确认导入\r\n    confirmImport() {\r\n      if (this.currentImportProduct) {\r\n        // 单个商品导入\r\n        this.currentImportProduct.importing = true\r\n        this.importDialogVisible = false\r\n\r\n        importAffiliateProducts({\r\n          productIds: [this.currentImportProduct.id],\r\n          operationUser: this.$store.getters.name || 'admin'\r\n        })\r\n        .then(data => {\r\n          // 根据导入结果显示不同的消息\r\n          if (data.failedCount === 0) {\r\n            if (data.skippedCount > 0) {\r\n              this.$message.warning(this.$t('affiliateProducts.importExists'))\r\n            } else {\r\n              this.$message.success(this.$t('affiliateProducts.importSuccess'))\r\n            }\r\n          } else {\r\n            this.$message.error(this.$t('affiliateProducts.importFailed', {\r\n              reason: data.failedProducts && data.failedProducts.length > 0 ? data.failedProducts[0].errorMessage : this.$t('common.unknownError')\r\n            }))\r\n          }\r\n        })\r\n        .catch(() => {\r\n          // 错误消息已在响应拦截器中处理\r\n        })\r\n        .finally(() => {\r\n          this.currentImportProduct.importing = false\r\n        })\r\n      } else {\r\n        // 批量导入\r\n        this.batchImporting = true\r\n        this.importDialogVisible = false\r\n\r\n        const productIds = this.selectedProducts.map(p => p.id)\r\n        importAffiliateProducts({\r\n          productIds,\r\n          operationUser: this.$store.getters.name || 'admin'\r\n        })\r\n        .then(data => {\r\n          // 根据导入结果显示不同的消息\r\n          if (data.failedCount === 0) {\r\n            if (data.skippedCount > 0 && data.successCount === 0) {\r\n              this.$message.warning(this.$t('affiliateProducts.batchImportExists'))\r\n            } else if (data.skippedCount > 0) {\r\n              this.$message.success(this.$t('affiliateProducts.batchImportMixed', {\r\n                success: data.successCount,\r\n                skipped: data.skippedCount\r\n              }))\r\n            } else {\r\n              this.$message.success(this.$t('affiliateProducts.batchImportSuccess', { count: data.successCount }))\r\n            }\r\n          } else if (data.successCount > 0) {\r\n            // 部分成功\r\n            this.$message.warning(this.$t('affiliateProducts.batchImportPartial', {\r\n              success: data.successCount,\r\n              failed: data.failedCount,\r\n              skipped: data.skippedCount\r\n            }))\r\n          } else {\r\n            // 全部失败\r\n            this.$message.error(this.$t('affiliateProducts.batchImportFailed', {\r\n              failed: data.failedCount,\r\n              skipped: data.skippedCount\r\n            }))\r\n          }\r\n\r\n          // 清空选择\r\n          this.$refs.productTable.clearSelection()\r\n        })\r\n        .catch(() => {\r\n          // 错误消息已在响应拦截器中处理\r\n        })\r\n        .finally(() => {\r\n          this.batchImporting = false\r\n        })\r\n      }\r\n    },\r\n  }\r\n}\r\n", null]}
<template>
  <div class="divBox relative">
    <!-- 搜索表单 -->
    <el-card class="box-card">
      <el-form :model="searchForm" :rules="searchRules" ref="searchFormRef" inline size="small">


        <el-form-item :label="$t('affiliateProducts.priceRange')">
          <el-input
            v-model="searchForm.priceMin"
            :placeholder="$t('affiliateProducts.minPrice')"
            style="width: 120px;"
          ></el-input>
          <span style="margin: 0 8px;">-</span>
          <el-input
            v-model="searchForm.priceMax"
            :placeholder="$t('affiliateProducts.maxPrice')"
            style="width: 120px;"
          ></el-input>
        </el-form-item>

        <el-form-item :label="$t('affiliateProducts.commissionRange')" prop="commissionRange">
          <el-input
            v-model="searchForm.commissionMin"
            :placeholder="$t('affiliateProducts.minCommission')"
            style="width: 120px;"
            @blur="validateCommissionRate('commissionMin')"
            @input="clearCommissionError('commissionMin')"
          ></el-input>
          <span style="margin: 0 8px;">-</span>
          <el-input
            v-model="searchForm.commissionMax"
            :placeholder="$t('affiliateProducts.maxCommission')"
            style="width: 120px;"
            @blur="validateCommissionRate('commissionMax')"
            @input="clearCommissionError('commissionMax')"
          ></el-input>
          <div v-if="commissionErrors.commissionMin" class="commission-error">
            {{ commissionErrors.commissionMin }}
          </div>
          <div v-if="commissionErrors.commissionMax" class="commission-error">
            {{ commissionErrors.commissionMax }}
          </div>
        </el-form-item>



        <el-form-item :label="$t('affiliateProducts.sort')">
          <el-select v-model="searchForm.sortField" style="width: 140px;">
            <el-option :label="$t('affiliateProducts.sortCommissionRate')" value="commission_rate"></el-option>
            <el-option :label="$t('affiliateProducts.sortCommission')" value="commission"></el-option>
            <el-option :label="$t('affiliateProducts.sortPrice')" value="product_sales_price"></el-option>
            <el-option :label="$t('affiliateProducts.sortSales')" value="units_sold"></el-option>
          </el-select>
          <el-select v-model="searchForm.sortOrder" style="width: 80px; margin-left: 8px;">
            <el-option :label="$t('affiliateProducts.sortDesc')" value="DESC"></el-option>
            <el-option :label="$t('affiliateProducts.sortAsc')" value="ASC"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('affiliateProducts.keywords')">
          <el-input
            v-model="currentKeyword"
            :placeholder="$t('affiliateProducts.addKeywordPlaceholder')"
            @keyup.enter.native="addKeyword"
            style="width: 300px;"
            clearable
            :disabled="searchForm.keywords.length >= 20"
          >
            <template slot="append">
              <span class="keyword-count" :class="{ 'keyword-count-warning': searchForm.keywords.length >= 18 }">
                {{ searchForm.keywords.length }}/20
              </span>
            </template>
          </el-input>
          <div v-if="keywordError" class="keyword-error" style="margin-top: 4px;">
            {{ keywordError }}
          </div>
        </el-form-item>

      </el-form>

      <!-- 操作按钮区域 -->
      <div class="search-actions">
        <el-button type="primary" @click="handleSearch">{{ $t('affiliateProducts.query') }}</el-button>
        <el-button @click="handleReset">{{ $t('affiliateProducts.reset') }}</el-button>
      </div>

      <!-- 关键词展示区域 -->
      <div class="keywords-display-section" v-if="searchForm.keywords.length > 0" style="margin-top: 16px;">
        <div class="keywords-display-header">
          <div class="keywords-display-label">{{ $t('affiliateProducts.selectedKeywords') }}</div>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="clearAllKeywords"
            class="clear-all-btn"
            :title="$t('affiliateProducts.clearAllKeywords')"
          >
            {{ $t('affiliateProducts.clearAll') }}
          </el-button>
        </div>
        <div class="keywords-display-container">
          <el-tag
            v-for="(keyword, index) in searchForm.keywords"
            :key="index"
            closable
            @close="removeKeyword(index)"
            class="keyword-display-tag"
          >
            {{ keyword }}
          </el-tag>
        </div>
      </div>
    </el-card>

    <!-- 产品列表 -->
    <el-card class="box-card" style="margin-top: 12px;">
      <div slot="header" class="clearfix">
        <span>{{ $t('affiliateProducts.listTitle') }}</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          @click="handleRefresh"
        >{{ $t('affiliateProducts.refresh') }}</el-button>
      </div>

      <!-- 批量操作按钮 -->
      <div v-if="hasSearched && tableData.length > 0" style="margin-bottom: 15px;">
        <el-button
          type="primary"
          size="small"
          @click="handleBatchImport"
          :disabled="selectedProducts.length === 0 || batchImporting"
        >
          {{ batchImporting ? $t('affiliateProducts.batchImporting') : `${$t('affiliateProducts.batchImport')} (${selectedProducts.length})` }}
        </el-button>
        <el-button
          type="danger"
          size="small"
          @click="handleBatchDelete"
          :disabled="selectedProducts.length === 0 || batchDeleting"
        >
          {{ batchDeleting ? $t('affiliateProducts.batchDeleting') : `${$t('affiliateProducts.batchDelete')} (${selectedProducts.length})` }}
        </el-button>
      </div>

      <!-- 未搜索时的提示 -->
      <div v-if="!hasSearched && tableData.length === 0" class="empty-tip">
        <el-empty :description="$t('affiliateProducts.emptyTip')"></el-empty>
      </div>

      <el-table
        ref="productTable"
        v-loading="loading"
        :data="tableData"
        size="small"
        :header-cell-style="{ fontWeight: 'bold' }"
        v-show="hasSearched"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column type="index" :label="$t('affiliateProducts.serialNumber')" width="60"></el-table-column>

        <el-table-column :label="$t('affiliateProducts.productImage')" width="100">
          <template slot-scope="scope">
            <el-image
              :src="scope.row.mainImageUrl"
              fit="cover"
              style="width: 60px; height: 60px; border-radius: 4px;"
              :preview-src-list="[scope.row.mainImageUrl]"
            >
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.productTitle')" min-width="200">
          <template slot-scope="scope">
            <el-link :href="scope.row.detailLink" target="_blank" type="primary">
              {{ scope.row.title }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.shop')" width="120">
          <template slot-scope="scope">
            {{ scope.row.shop ? scope.row.shop.name : '-' }}
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.originalPrice')" width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.originalPrice">
              {{ formatPrice(scope.row.originalPrice) }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.salesPrice')" width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.salesPrice">
              {{ formatPrice(scope.row.salesPrice) }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.commissionRate')" width="80">
          <template slot-scope="scope">
            <span v-if="scope.row.commission">
              {{ formatCommissionRate(scope.row.commission.rate) }}%
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.commissionAmount')" width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.commission">
              {{ scope.row.commission.amount }} {{ scope.row.commission.currency }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.unitsSold')" width="80">
          <template slot-scope="scope">
            {{ scope.row.unitsSold || 0 }}
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.inventoryStatus')" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.hasInventory ? 'success' : 'danger'" size="mini">
              {{ scope.row.hasInventory ? $t('affiliateProducts.hasInventory') : $t('affiliateProducts.noInventory') }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.saleRegion')" width="80">
          <template slot-scope="scope">
            {{ scope.row.saleRegion || '-' }}
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.importStatus')" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.isImported ? 'success' : 'info'" size="mini">
              {{ scope.row.isImported ? $t('affiliateProducts.imported') : $t('affiliateProducts.notImported') }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column :label="$t('affiliateProducts.action')" width="180" fixed="right">
          <template slot-scope="scope">
            <el-button
              :type="scope.row.isImported ? 'success' : 'primary'"
              size="mini"
              @click="handleImportProduct(scope.row)"
              :disabled="scope.row.importing || scope.row.isImported"
            >
              {{ scope.row.importing ? $t('affiliateProducts.importing') : (scope.row.isImported ? $t('affiliateProducts.imported') : $t('affiliateProducts.import')) }}
            </el-button>
            <el-button
              type="danger"
              size="mini"
              @click="handleDeleteProduct(scope.row)"
              :disabled="scope.row.deleting"
            >
              {{ scope.row.deleting ? $t('affiliateProducts.deleting') : $t('affiliateProducts.delete') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div v-if="hasSearched" class="pagination-container" style="margin-top: 20px; text-align: center;">
        <el-button
          :disabled="!hasPrevPage"
          @click="handlePrevPage"
          size="small"
        >{{ $t('affiliateProducts.prevPage') }}</el-button>
        <el-button
          :disabled="!hasNextPage"
          @click="handleNextPage"
          size="small"
        >{{ $t('affiliateProducts.nextPage') }}</el-button>
        <span style="margin-left: 20px;">
          {{ $t('affiliateProducts.pageSize') }}
          <el-select v-model="searchForm.pageSize" size="mini" style="width: 80px;" @change="handleSearch">
            <el-option label="10" :value="10"></el-option>
            <el-option label="20" :value="20"></el-option>
            <el-option label="50" :value="50"></el-option>
          </el-select>
        </span>
        <span style="margin-left: 20px;">
          {{ $t('affiliateProducts.totalCount', { count: totalCount }) }}
        </span>
      </div>
    </el-card>

    <!-- 导入确认对话框 -->
    <el-dialog
      :title="currentImportProduct ? $t('affiliateProducts.importSingle') : $t('affiliateProducts.importBatch')"
      :visible.sync="importDialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <div v-if="currentImportProduct">
        <p><strong>{{ $t('affiliateProducts.productTitle') }}：</strong>{{ currentImportProduct.title }}</p>
        <p><strong>{{ $t('product.productId') }}：</strong>{{ currentImportProduct.id }}</p>
      </div>
      <div v-else>
        <p><strong>{{ $t('affiliateProducts.selectedCount') }}</strong>{{ selectedProducts.length }}</p>
      </div>
      <div style="margin: 20px 0; padding: 15px; background-color: #f0f9ff; border: 1px solid #b3d8ff; border-radius: 4px;">
        <i class="el-icon-info" style="color: #409eff; margin-right: 8px;"></i>
        <span style="color: #409eff;">{{ $t('affiliateProducts.brandAutoDetect') }}</span>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="importDialogVisible = false">{{ $t('affiliateProducts.cancel') }}</el-button>
        <el-button type="primary" @click="confirmImport">{{ $t('affiliateProducts.confirmImport') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { searchAffiliateProducts } from '@/api/tiktok'
import { importAffiliateProducts, deleteAffiliateProducts } from '@/api/affiliate-products'

export default {
  name: 'AffiliateProducts',
  data() {
    return {
      loading: false,
      tableData: [],
      searchForm: {
        keywords: [], // 改为数组存储多个关键词
        priceMin: '',
        priceMax: '',
        commissionMin: '',
        commissionMax: '',

        sortField: 'commission_rate',
        sortOrder: 'DESC',
        pageSize: 10 // 默认分页数量改为10
      },
      searchRules: {
        // 移除关键词必填验证
      },
      currentCursor: '',
      nextPageToken: '',
      prevPageTokens: [], // 存储前面页面的token，用于返回上一页
      totalCount: 0,
      hasNextPage: false,
      hasPrevPage: false,
      hasSearched: false, // 标记是否已经搜索过
      selectedProducts: [], // 选中的商品列表
      batchImporting: false, // 批量入库状态
      batchDeleting: false, // 批量删除状态
      importDialogVisible: false, // 入库确认对话框
      currentImportProduct: null, // 当前导入的商品（单个导入时使用）
      commissionErrors: { // 佣金率验证错误信息
        commissionMin: '',
        commissionMax: ''
      },
      currentKeyword: '', // 当前输入的关键词
      keywordError: '', // 关键词验证错误信息

    }
  },
  mounted() {
    // 页面加载时不自动搜索，等待用户点击查询按钮
  },
  methods: {
    // 佣金率验证
    validateCommissionRate(field) {
      const value = this.searchForm[field]
      if (value === '' || value === null || value === undefined) {
        this.commissionErrors[field] = ''
        return true
      }

      const numValue = parseFloat(value)
      if (isNaN(numValue)) {
        this.commissionErrors[field] = this.$t('affiliateProducts.commissionInvalidNumber')
        return false
      }

      if (numValue !== 0 && numValue < 1000) {
        this.commissionErrors[field] = this.$t('affiliateProducts.commissionRangeError')
        return false
      }

      this.commissionErrors[field] = ''
      return true
    },

    // 清除佣金率错误信息
    clearCommissionError(field) {
      this.commissionErrors[field] = ''
    },

    // 添加关键词
    addKeyword() {
      const keyword = this.currentKeyword.trim()
      if (!keyword) {
        this.keywordError = ''
        return
      }

      // 验证关键词数量限制
      if (this.searchForm.keywords.length >= 20) {
        this.keywordError = this.$t('affiliateProducts.keywordLimitExceeded')
        return
      }

      // 验证关键词长度
      if (keyword.length > 255) {
        this.keywordError = this.$t('affiliateProducts.keywordTooLong')
        return
      }

      // 检查是否重复
      if (this.searchForm.keywords.includes(keyword)) {
        this.keywordError = this.$t('affiliateProducts.keywordDuplicate')
        return
      }

      // 添加关键词
      this.searchForm.keywords.push(keyword)
      this.currentKeyword = ''
      this.keywordError = ''
    },

    // 移除关键词
    removeKeyword(index) {
      this.searchForm.keywords.splice(index, 1)
      this.keywordError = ''
    },

    // 清理所有关键词
    clearAllKeywords() {
      this.$confirm(
        this.$t('affiliateProducts.confirmClearAllKeywords'),
        this.$t('affiliateProducts.clearAllKeywords'),
        {
          confirmButtonText: this.$t('common.confirm'),
          cancelButtonText: this.$t('common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        this.searchForm.keywords = []
        this.keywordError = ''
        this.$message.success(this.$t('affiliateProducts.keywordsClearedSuccess'))
      }).catch(() => {
        // 用户取消操作
      })
    },



    // 搜索
    handleSearch() {
      // 验证佣金率字段
      const isCommissionMinValid = this.validateCommissionRate('commissionMin')
      const isCommissionMaxValid = this.validateCommissionRate('commissionMax')

      if (!isCommissionMinValid || !isCommissionMaxValid) {
        this.$message.error(this.$t('affiliateProducts.pleaseFixErrors'))
        return
      }

      this.currentCursor = ''
      this.nextPageToken = ''
      this.prevPageTokens = []
      this.hasPrevPage = false
      this.hasSearched = true
      this.loadData()
    },

    // 重置
    handleReset() {
      this.searchForm = {
        keywords: [], // 重置为空数组
        priceMin: '',
        priceMax: '',
        commissionMin: '',
        commissionMax: '',

        sortField: 'commission_rate',
        sortOrder: 'DESC',
        pageSize: 10 // 重置时也使用默认分页数量10
      }
      // 清除错误信息
      this.commissionErrors = {
        commissionMin: '',
        commissionMax: ''
      }
      this.currentKeyword = ''
      this.keywordError = ''
      // 重置表单（无需验证状态重置）
      // 清空表格数据
      this.tableData = []
      this.hasSearched = false
      this.totalCount = 0
      this.hasNextPage = false
      this.hasPrevPage = false
      this.currentCursor = ''
      this.nextPageToken = ''
      this.prevPageTokens = []
    },

    // 刷新
    handleRefresh() {
      if (this.hasSearched) {
        this.loadData()
      } else {
        this.$message.warning(this.$t('affiliateProducts.searchFirst'))
      }
    },

    // 下一页
    handleNextPage() {
      if (this.hasNextPage && this.nextPageToken) {
        this.prevPageTokens.push(this.currentCursor)
        this.currentCursor = this.nextPageToken
        this.hasPrevPage = true
        this.loadData()
      }
    },

    // 上一页
    handlePrevPage() {
      if (this.hasPrevPage && this.prevPageTokens.length > 0) {
        this.currentCursor = this.prevPageTokens.pop()
        if (this.prevPageTokens.length === 0) {
          this.hasPrevPage = false
        }
        this.loadData()
      }
    },

    // 加载数据
    loadData() {
      this.loading = true

      // 构建请求参数 - 与V202405 AffiliateProductSearchRequest保持完全一致
      const params = {
        // 基础分页和排序参数
        pageSize: this.searchForm.pageSize,
        cursor: this.currentCursor,
        sortField: this.searchForm.sortField,
        sortOrder: this.searchForm.sortOrder,

        // 搜索过滤参数（按V202405 API字段名）
        titleKeywords: this.searchForm.keywords.length > 0 ? this.searchForm.keywords : null,
        salesPriceMin: this.searchForm.priceMin ? parseFloat(this.searchForm.priceMin) : null,
        salesPriceMax: this.searchForm.priceMax ? parseFloat(this.searchForm.priceMax) : null,
        commissionRateMin: this.searchForm.commissionMin ? parseFloat(this.searchForm.commissionMin) : null,
        commissionRateMax: this.searchForm.commissionMax ? parseFloat(this.searchForm.commissionMax) : null,

      }

      searchAffiliateProducts(params)
        .then(res => {
          this.tableData = res.products || []
          this.nextPageToken = res.nextPageToken || ''
          this.totalCount = res.totalCount || 0
          this.hasNextPage = !!this.nextPageToken

          if (this.tableData.length === 0) {
            this.$message.info(this.$t('affiliateProducts.noResults'))
          }
        })
        .catch(() => {
          this.loading = false
        })
        .finally(() => {
          this.loading = false
        })
    },

    // 格式化价格
    formatPrice(priceObj) {
      if (!priceObj) return '-'
      const min = priceObj.minimumAmount
      const max = priceObj.maximumAmount
      const currency = priceObj.currency

      if (min === max) {
        return `${min} ${currency}`
      } else {
        return `${min} - ${max} ${currency}`
      }
    },

    // 格式化佣金率（基点转百分比）
    formatCommissionRate(rate) {
      if (!rate) return '0'
      return (rate / 100).toFixed(2)
    },

    // 处理选择变化
    handleSelectionChange(selection) {
      this.selectedProducts = selection
    },

    // 单个商品导入
    handleImportProduct(product) {
      // 设置当前操作的商品
      this.currentImportProduct = product
      this.importDialogVisible = true
    },

    // 单个商品删除
    handleDeleteProduct(product) {
      this.$confirm(this.$t('affiliateProducts.deleteConfirm'), this.$t('common.deleteConfirm'), {
        confirmButtonText: this.$t('common.confirm'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      })
      .then(() => {
        product.deleting = true

        deleteAffiliateProducts([product.id])
        .then(() => {
          this.$message.success(this.$t('affiliateProducts.deleteSuccess'))
        })
        .catch(() => {
          // 错误消息已在响应拦截器中处理
        })
        .finally(() => {
          product.deleting = false
        })
      })
      .catch(() => {
        // 用户取消删除，不需要处理
      })
    },

    // 批量导入
    handleBatchImport() {
      if (this.selectedProducts.length === 0) {
        this.$message.warning(this.$t('affiliateProducts.selectFirst'))
        return
      }

      // 设置批量导入模式
      this.currentImportProduct = null
      this.importDialogVisible = true
    },

    // 批量删除
    handleBatchDelete() {
      if (this.selectedProducts.length === 0) {
        this.$message.warning(this.$t('affiliateProducts.selectDeleteFirst'))
        return
      }

      this.$confirm(this.$t('affiliateProducts.batchDeleteConfirm', { count: this.selectedProducts.length }), this.$t('affiliateProducts.batchDelete'), {
        confirmButtonText: this.$t('common.confirm'),
        cancelButtonText: this.$t('common.cancel'),
        type: 'warning'
      })
      .then(() => {
        this.batchDeleting = true

        const productIds = this.selectedProducts.map(p => p.id)
        deleteAffiliateProducts(productIds)
        .then(() => {
          this.$message.success(this.$t('affiliateProducts.batchDeleteSuccess'))
          // 清空选择
          this.$refs.productTable.clearSelection()
        })
        .catch(() => {
          // 错误消息已在响应拦截器中处理
        })
        .finally(() => {
          this.batchDeleting = false
        })
      })
      .catch(() => {
        // 用户取消删除，不需要处理
      })
    },



    // 确认导入
    confirmImport() {
      if (this.currentImportProduct) {
        // 单个商品导入
        this.currentImportProduct.importing = true
        this.importDialogVisible = false

        importAffiliateProducts({
          productIds: [this.currentImportProduct.id],
          operationUser: this.$store.getters.name || 'admin'
        })
        .then(data => {
          // 根据导入结果显示不同的消息
          if (data.failedCount === 0) {
            if (data.skippedCount > 0) {
              this.$message.warning(this.$t('affiliateProducts.importExists'))
            } else {
              this.$message.success(this.$t('affiliateProducts.importSuccess'))
            }
          } else {
            this.$message.error(this.$t('affiliateProducts.importFailed', {
              reason: data.failedProducts && data.failedProducts.length > 0 ? data.failedProducts[0].errorMessage : this.$t('common.unknownError')
            }))
          }
        })
        .catch(() => {
          // 错误消息已在响应拦截器中处理
        })
        .finally(() => {
          this.currentImportProduct.importing = false
        })
      } else {
        // 批量导入
        this.batchImporting = true
        this.importDialogVisible = false

        const productIds = this.selectedProducts.map(p => p.id)
        importAffiliateProducts({
          productIds,
          operationUser: this.$store.getters.name || 'admin'
        })
        .then(data => {
          // 根据导入结果显示不同的消息
          if (data.failedCount === 0) {
            if (data.skippedCount > 0 && data.successCount === 0) {
              this.$message.warning(this.$t('affiliateProducts.batchImportExists'))
            } else if (data.skippedCount > 0) {
              this.$message.success(this.$t('affiliateProducts.batchImportMixed', {
                success: data.successCount,
                skipped: data.skippedCount
              }))
            } else {
              this.$message.success(this.$t('affiliateProducts.batchImportSuccess', { count: data.successCount }))
            }
          } else if (data.successCount > 0) {
            // 部分成功
            this.$message.warning(this.$t('affiliateProducts.batchImportPartial', {
              success: data.successCount,
              failed: data.failedCount,
              skipped: data.skippedCount
            }))
          } else {
            // 全部失败
            this.$message.error(this.$t('affiliateProducts.batchImportFailed', {
              failed: data.failedCount,
              skipped: data.skippedCount
            }))
          }

          // 清空选择
          this.$refs.productTable.clearSelection()
        })
        .catch(() => {
          // 错误消息已在响应拦截器中处理
        })
        .finally(() => {
          this.batchImporting = false
        })
      }
    },
  }
}
</script>

<style scoped>
.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.empty-tip {
  padding: 40px 0;
  text-align: center;
}

.commission-error {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 4px;
  line-height: 1.2;
}

/* 关键词展示区域样式 */
.keywords-display-section {
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.keywords-display-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.keywords-display-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.clear-all-btn {
  color: #f56c6c;
  padding: 0;
  font-size: 12px;
}

.clear-all-btn:hover {
  color: #f78989;
}

.keywords-display-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.keyword-display-tag {
  margin: 0;
}

/* 关键词输入框样式 */
.keyword-error {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1.2;
}

.keyword-count {
  font-size: 12px;
  color: #909399;
  padding: 0 12px;
  white-space: nowrap;
  background-color: #f5f7fa;
}

.keyword-count-warning {
  color: #e6a23c;
  font-weight: bold;
  background-color: #fdf6ec;
}

/* 搜索操作按钮区域 */
.search-actions {
  margin-top: 16px;
  padding-left: 16px;
  display: flex;
  gap: 8px;
}
</style>

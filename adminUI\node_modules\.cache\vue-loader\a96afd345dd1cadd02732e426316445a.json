{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\systemSetting\\administratorAuthority\\identityManager\\index.vue?vue&type=template&id=269441c1&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\systemSetting\\administratorAuthority\\identityManager\\index.vue", "mtime": 1754373233898}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754052682065}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <el-card class=\"box-card\">\n    <el-form inline size=\"small\" @submit.native.prevent>\n      <el-form-item>\n        <el-input\n          v-model=\"listPram.roleName\"\n          :placeholder=\"$t('admin.system.role.roleName')\"\n          clearable\n          class=\"selWidth\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button\n          size=\"mini\"\n          type=\"primary\"\n          @click.native=\"handleGetRoleList\"\n          >{{ $t(\"common.query\") }}</el-button\n        >\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">{{\n          $t(\"common.reset\")\n        }}</el-button>\n      </el-form-item>\n    </el-form>\n    <el-form inline @submit.native.prevent>\n      <el-form-item>\n        <el-button\n          size=\"mini\"\n          type=\"primary\"\n          @click=\"handlerOpenEdit(0)\"\n          v-hasPermi=\"[\n            'admin:system:role:save',\n            'admin:system:menu:cache:tree'\n          ]\"\n          >{{ $t(\"admin.system.role.addRole\") }}</el-button\n        >\n      </el-form-item>\n    </el-form>\n    <el-table\n      :data=\"listData.list\"\n      size=\"mini\"\n      :header-cell-style=\"{\n        fontWeight: 'bold',\n        background: '#f8f8f9',\n        color: '#515a6e',\n        height: '40px'\n      }\"\n    >\n      <el-table-column\n        :label=\"$t('admin.system.role.roleId')\"\n        prop=\"id\"\n        width=\"120\"\n      ></el-table-column>\n      <el-table-column\n        :label=\"$t('admin.system.role.roleName')\"\n        prop=\"roleName\"\n        min-width=\"130\"\n      />\n      <el-table-column :label=\"$t('common.status')\" prop=\"status\">\n        <template\n          slot-scope=\"scope\"\n          v-if=\"checkPermi(['admin:system:role:update:status'])\"\n        >\n          <el-switch\n            v-model=\"scope.row.status\"\n            :active-value=\"true\"\n            :inactive-value=\"false\"\n            style=\"width:40px;\"\n            @change=\"handleStatusChange(scope.row)\"\n          ></el-switch>\n        </template>\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('admin.system.role.createTime')\"\n        prop=\"createTime\"\n        min-width=\"150\"\n      />\n      <el-table-column\n        :label=\"$t('admin.system.role.updateTime')\"\n        prop=\"updateTime\"\n        min-width=\"150\"\n      />\n      <el-table-column\n        :label=\"$t('admin.system.role.operation')\"\n        min-width=\"130\"\n        fixed=\"right\"\n      >\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"small\"\n            type=\"text\"\n            @click=\"handlerOpenEdit(1, scope.row)\"\n            v-hasPermi=\"['admin:system:role:info']\"\n            >{{ $t(\"admin.system.role.editRole\") }}</el-button\n          >\n          <el-button\n            size=\"small\"\n            type=\"text\"\n            @click=\"handlerOpenDel(scope.row)\"\n            v-hasPermi=\"['admin:system:role:delete']\"\n            >{{ $t(\"admin.system.role.deleteRole\") }}</el-button\n          >\n        </template>\n      </el-table-column>\n    </el-table>\n    <el-pagination\n      :current-page=\"listPram.page\"\n      :page-sizes=\"constants.page.limit\"\n      :layout=\"constants.page.layout\"\n      :total=\"listData.total\"\n      @size-change=\"handleSizeChange\"\n      @current-change=\"handleCurrentChange\"\n    />\n  </el-card>\n  <el-dialog\n    :visible.sync=\"editDialogConfig.visible\"\n    :title=\"\n      editDialogConfig.isCreate === 0\n        ? $t('admin.system.role.createIdentity')\n        : $t('admin.system.role.editIdentity')\n    \"\n    destroy-on-close\n    :close-on-click-modal=\"false\"\n    width=\"500px\"\n  >\n    <edit\n      v-if=\"editDialogConfig.visible\"\n      :is-create=\"editDialogConfig.isCreate\"\n      :edit-data=\"editDialogConfig.editData\"\n      @hideEditDialog=\"hideEditDialog\"\n      ref=\"editForm\"\n    />\n  </el-dialog>\n</div>\n", null]}
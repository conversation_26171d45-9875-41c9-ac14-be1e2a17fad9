{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\lang\\id.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\lang\\id.js", "mtime": 1754377406951}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754052678844}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\eslint-loader\\index.js", "mtime": 1754052677050}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _product, _request, _history, _admin, _affiliateProducts, _common$navbar$common;\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nvar _default = exports.default = (_common$navbar$common = {\n  common: {\n    confirm: \"Konfirmasi\",\n    cancel: \"Batal\",\n    tip: \"Tips\",\n    cancelled: \"Dibatalkan\",\n    deleteFile: \"Hapus file ini secara permanen\",\n    systemTip: \"Tips Sistem\"\n  },\n  navbar: {\n    home: \"Beranda\",\n    profile: \"Profil\",\n    logout: \"Keluar\"\n  }\n}, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_common$navbar$common, \"common\", _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({\n  editSuccess: \"Berhasil diubah\",\n  addSuccess: \"Berhasil ditambahkan\",\n  confirmDelete: \"Apakah Anda yakin ingin menghapus data bernama '{name}'?\",\n  status: \"Status\",\n  fetchDataFailed: \"Gagal mengambil data\",\n  operationSuccess: \"Operasi berhasil\",\n  operationFailed: \"Operasi gagal\",\n  unknownError: \"Kesalahan Tidak Diketahui\",\n  confirm: \"Konfirmasi\",\n  cancel: \"Batal\",\n  deleteConfirm: \"Yakin hapus?\",\n  deleteSuccess: \"Berhasil dihapus\",\n  deleteFailed: \"Gagal menghapus\",\n  saveSuccess: \"Berhasil disimpan\",\n  saveFailed: \"Gagal menyimpan\",\n  enterRejectReason: \"Masukkan alasan penolakan\",\n  startDate: \"Tanggal Mulai\",\n  endDate: \"Tanggal Akhir\",\n  all: \"Semua\",\n  serialNumber: \"Nomor Urut\",\n  query: \"Cari\",\n  reset: \"Reset\",\n  enter: \"Masukkan\",\n  pendingReview: \"Menunggu Review\",\n  reviewedPassed: \"Telah Disetujui\",\n  reviewedRejected: \"Telah Ditolak\",\n  pleaseSelect: \"Silakan Pilih\",\n  yes: \"Ya\",\n  no: \"Tidak\",\n  show: \"Tampilkan\",\n  hide: \"Sembunyikan\",\n  unknown: \"Tidak Dikenal\",\n  keyword: {\n    text: \"Pesan Teks\",\n    image: \"Pesan Gambar\",\n    news: \"Pesan Gambar & Teks\",\n    voice: \"Pesan Suara\"\n  },\n  couponType: {\n    general: \"Kupon Umum\",\n    product: \"Kupon Produk\",\n    category: \"Kupon Kategori\"\n  },\n  couponReceive: {\n    manual: \"Ambil Manual\",\n    newUser: \"Kupon Pengguna Baru\",\n    gift: \"Kupon Hadiah\"\n  },\n  paymentStatus: {\n    unpaid: \"Belum Dibayar\",\n    paid: \"Sudah Dibayar\"\n  },\n  withdrawType: {\n    bank: \"Kartu Bank\",\n    alipay: \"Alipay\",\n    wechat: \"WeChat\"\n  },\n  rechargeType: {\n    wechatPublic: \"Akun Resmi WeChat\",\n    wechatH5: \"Pembayaran H5 WeChat\",\n    miniProgram: \"Program Mini\"\n  },\n  withdrawStatus: {\n    rejected: \"Ditolak\",\n    reviewing: \"Dalam Tinjauan\",\n    withdrawn: \"Telah Dicairkan\"\n  }\n}, \"status\", {\n  bargain: {\n    1: \"Berlangsung\",\n    2: \"Belum Selesai\",\n    3: \"Berhasil\"\n  }\n}), \"onePass\", {\n  sms: \"SMS\",\n  copy: \"Pengumpulan Produk\",\n  expr_query: \"Inquiry Logistik\",\n  expr_dump: \"Cetak Resi Elektronik\"\n}), \"editStatus\", {\n  1: \"Belum Diperiksa\",\n  2: \"Dalam Tinjauan\",\n  3: \"Tinjauan Gagal\",\n  4: \"Berhasil Diperiksa\"\n}), \"videoStatus\", {\n  0: \"Nilai Awal\",\n  5: \"Diterbitkan\",\n  11: \"Ditarik Sendiri\",\n  13: \"Dihapus karena Pelanggaran / Dihapus Sistem Risiko\"\n}), \"actions\", \"Tindakan\")), \"appMain\", {\n  copyright: \"Hak Cipta © 2025\"\n}), \"dashboard\", {\n  home: \"Beranda\",\n  brandCenter: \"Pusat Merek\",\n  brandManage: \"Manajemen Merek\",\n  productManage: \"Manajemen Produk\",\n  appManage: \"Manajemen Aplikasi\",\n  homeManage: \"Manajemen Halaman Utama\",\n  opsCenter: \"Pusat Operasi\",\n  affiliateProducts: \"Produk Afiliasi\",\n  withdrawalReview: \"Pemeriksaan Penarikan\",\n  withdrawalRecords: \"Catatan Penarikan\",\n  orderCenter: \"Pusat Pesanan\",\n  orderInquiry: \"Pencarian Pesanan\",\n  userCenter: \"Pusat Pengguna\",\n  userManage: \"Manajemen Pengguna\",\n  userLevel: \"Level Pengguna\",\n  levelUpgradeOrder: \"Pesanan Peningkatan Level\",\n  financeCenter: \"Pusat Keuangan\",\n  financeDetails: \"Rincian Keuangan\",\n  withdrawalRequest: \"Permintaan Penarikan\",\n  paramSettings: \"Pengaturan Parameter\",\n  rewardRules: \"Pengaturan Aturan Hadiah\",\n  withdrawalFee: \"Pengaturan Biaya Penarikan\",\n  referralRewardConfig: \"Konfigurasi Hadiah Rujukan\",\n  membershipFee: \"Pengaturan Biaya Peningkatan Keanggotaan\",\n  accountCenter: \"Pusat Akun\",\n  adminPermissions: \"Izin Administrasi\",\n  roleManage: \"Manajemen Peran\",\n  adminList: \"Daftar Administrator\",\n  permissionRules: \"Aturan Izin\",\n  profile: \"Profil\",\n  systemSettings: \"Pengaturan Sistem\",\n  chainTransferRecord: \"Rekam beralih rantai\",\n  platformCashbackRate: \"Pengaturan Tingkat Pengembalian Platform\",\n  shoppingCashbackRules: \"Aturan Cashback Belanja\"\n}), \"platformCashbackRate\", {\n  platformCashbackRate: \"Platform Cashback Rate\",\n  editTitle: \"Edit Platform Cashback Rate\",\n  addTitle: \"Add Platform Cashback Rate\",\n  placeholder: {\n    platformCashbackRate: \"Please enter platform cashback rate\"\n  }\n}), \"tagsView\", {\n  refresh: \"Segarkan\",\n  close: \"Tutup\",\n  closeOthers: \"Tutup Lainnya\",\n  closeAll: \"Tutup Semua\"\n}), \"homepage\", {\n  welcome: \"Selamat datang di Panel Admin GENCO!\",\n  paymentSwitch: \"Pengaturan Pembayaran\",\n  paymentSwitchTip1: \"Jika diaktifkan, frontend mengizinkan pengguna menjadi agen dan mitra.\",\n  paymentSwitchTip2: \"Jika dinonaktifkan, peningkatan tidak diperbolehkan. Pengaturan ini hanya untuk\",\n  paymentSwitchTip3: \"kemudahan pengajuan ke AppStore. Jangan diubah sembarangan.\",\n  loginMode: \"Metode Login\",\n  loginModeTip1: \"Pengaturan ini hanya mengontrol tampilan login TikTok dan login SMS\",\n  loginModeTip2: \"di halaman login untuk kemudahan pengajuan ke AppStore. Jangan diubah sembarangan.\",\n  tikTokLogin: \"Login TikTok\",\n  smsLogin: \"Login SMS\",\n  submit: \"Simpan\"\n}), \"brand\", {\n  search: \"Cari Merek:\",\n  status: \"Status:\",\n  pleaseSelect: \"Silakan Pilih\",\n  reset: \"Reset\",\n  query: \"Cari\",\n  addBrand: \"Tambah Merek\",\n  batchOnline: \"Aktifkan Semua\",\n  batchOffline: \"Nonaktifkan Semua\",\n  batchDelete: \"Hapus Semua\",\n  brandLogo: \"Logo Merek\",\n  brandName: \"Nama Merek\",\n  industry: \"Industri\",\n  platform: \"Platform\",\n  productCount: \"Jumlah Produk\",\n  maxCashback: \"Rate Cashback Tertinggi\",\n  soldCount: \"Jumlah Produk Terjual\",\n  soldAmount: \"Jumlah Penjualan (Rp)\",\n  cashbackAmount: \"Jumlah Cashback (Rp)\",\n  shareCount: \"Jumlah Bagi\",\n  createTime: \"Waktu Dibuat\",\n  creator: \"Pembuat\",\n  statusLabel: \"Status\",\n  isHot: \"Apakah Merek Populer\",\n  isHighCashback: \"Apakah Merek Cashback Tinggi\",\n  offline: \"Nonaktif\",\n  online: \"Aktif\",\n  edit: \"Ubah\",\n  delete: \"Hapus\",\n  addDialogTitle: \"Tambah Merek\",\n  editDialogTitle: \"Ubah Merek\",\n  brandNameInput: \"Masukkan nama merek\",\n  brandLogoInput: \"Masukkan URL gambar\",\n  contactPerson: \"Kontak:\",\n  contactPhone: \"Telepon Kontak:\",\n  confirm: \"Simpan\",\n  update: \"Perbarui\",\n  cancel: \"Batal\",\n  platformTiktok: \"TikTok\",\n  platformShopee: \"Shopee\",\n  confirmOperation: \"Apakah Anda yakin ingin melakukan operasi ini?\",\n  prompt: \"Peringatan\",\n  productList: \"Product\",\n  isOnline: \"On Sale\",\n  isOutline: \"Pending\",\n  isOuted: \"Offline\",\n  selectTip: \"Silakan pilih\",\n  refreshBrands: \"Segarkan Data Merek\",\n  refreshingBrands: \"Menyegarkan data merek...\"\n}), \"product\", (_product = {\n  search: \"Cari Produk:\",\n  keywordsPlaceholder: \"Masukkan nama atau kata kunci produk\",\n  status: \"Status:\",\n  pleaseSelect: \"Silakan Pilih\",\n  query: \"Cari\",\n  reset: \"Reset\",\n  addProduct: \"Tambah Produk\",\n  batchOnline: \"Aktifkan Semua\",\n  batchOffline: \"Nonaktifkan Semua\",\n  batchDelete: \"Hapus Semua\",\n  productImage: \"Gambar Produk\",\n  productName: \"Nama Produk\",\n  productPrice: \"Harga Produk\",\n  cashbackRate: \"Rate Cashback\",\n  estimatedCashback: \"Jumlah Cashback (Rp)\",\n  productLink: \"Tautan Produk\",\n  shareCount: \"Jumlah Bagi\",\n  soldCount: \"Jumlah Terjual\",\n  cashbackAmount: \"Jumlah Cashback (Rp)\",\n  addTime: \"Waktu Penambahan\",\n  action: \"Aksi\",\n  offline: \"Nonaktif\",\n  online: \"Aktif\",\n  edit: \"Ubah\",\n  delete: \"Hapus\",\n  isHot: \"Populer\",\n  isBenefit: \"Cashback Tinggi\",\n  isTikTok: \"TikTok\",\n  addDialogTitle: \"Tambah Produk\",\n  editDialogTitle: \"Edit Produk\",\n  enterProductLink: \"Masukkan tautan produk\",\n  fetchProductInfo: \"Ambil Info Produk\",\n  enterProductName: \"Masukkan nama produk\"\n}, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_product, \"productPrice\", \"Harga Produk\"), \"enterProductPrice\", \"Masukkan harga produk\"), \"enterCashbackRate\", \"Masukkan rate cashback\"), \"enterCashbackAmount\", \"Masukkan jumlah cashback\"), \"isOnline\", \"Apakah Aktif:\"), \"yes\", \"Ya\"), \"no\", \"Tidak\"), \"confirm\", \"Simpan\"), \"cancel\", \"Batal\"), \"all\", \"Semua\"), _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_product, \"fetchProductFailed\", \"Gagal mengambil info produk\"), \"isOnIndex\", \"Tampilkan di beranda?\"), \"usercashbackRate\", \"User Cashback Rate\"), \"isOnline\", \"On Sale\"), \"isOutline\", \"Pending\"), \"isOuted\", \"Offline\"))), \"operations\", {\n  withdrawal: {\n    walletWithdrawal: \"Penarikan Dompet Elektronik\",\n    bankWithdrawal: \"Penarikan Bank\",\n    applicant: \"Pemohon\",\n    applicationTime: \"Waktu Pengajuan\",\n    electronicWallet: \"Dompet Elektronik\",\n    bankName: \"Nama Bank\",\n    applicationId: \"ID Pengajuan\",\n    applicantName: \"Pemohon\",\n    withdrawalAmount: \"Jumlah Penarikan\",\n    serviceFee: \"Biaya Layanan\",\n    actualAmount: \"Jumlah yang Diterima\",\n    walletCode: \"Kode Dompet\",\n    walletAccount: \"Akun\",\n    bankCardNumber: \"Nomor Kartu Bank\",\n    name: \"Nama\",\n    phoneNumber: \"Nomor Telepon\",\n    withdrawalCount: \"Jumlah Riwayat Penarikan\",\n    auditResult: \"Hasil Audit\",\n    rejectReason: \"Alasan Penolakan\",\n    approve: \"Setujui\",\n    reject: \"Tolak\",\n    rejectReview: \"Tolak Audit\",\n    exportExcel: \"Ekspor ke Excel\",\n    transferTime: \"Waktu Transfer\",\n    transferResult: \"Hasil Transfer\",\n    remark: \"Catatan\",\n    attachment: \"Lampiran\",\n    operator: \"Operator\",\n    withdrawalStatus: \"Status Penarikan\",\n    ShopeePay: \"ShopeePay\",\n    DANA: \"DANA\",\n    OVO: \"OVO\",\n    Gopay: \"Gopay\",\n    unapproved: \"Tidak Disetujui\",\n    underReview: \"Dalam Tinjauan\",\n    reviewed: \"Telah Diperiksa\",\n    paid: \"Telah Dibayarkan\"\n  }\n}), \"order\", {\n  search: {\n    orderNo: \"Order Number\",\n    productTitle: \"Product Name\",\n    status: \"Status\",\n    all: \"All\",\n    query: \"Query\",\n    reset: \"Reset\",\n    exportExcel: \"Export Excel\",\n    serialNumber: \"Serial Number\",\n    productImage: \"Product Image\",\n    orderId: \"Order ID\",\n    productName: \"Product Name\",\n    payCount: \"Purchase Count\",\n    actualCommission: \"Product Price (Rp)\",\n    payPrice: \"Order Amount (Rp)\",\n    commissionRate: \"Product Cashback Rate\",\n    estimatedCommission: \"Estimated Cashback (Rp)\",\n    contentId: \"E-commerce Platform\",\n    statusLabel: \"Order Status\",\n    unknown: \"Unknown\",\n    ordered: \"Ordered\",\n    settled: \"Settled\",\n    refunded: \"Refunded\",\n    frozen: \"Frozen\",\n    deducted: \"Deducted\",\n    totalPrice: \"Jumlah\",\n    userCashBackRate: \"Tingkat Cashback Pengguna\",\n    creatTime: \"Waktu Pemesanan\"\n  }\n}), _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_common$navbar$common, \"user\", {\n  center: {\n    nickname: \"Nama Panggilan\",\n    phone: \"Nomor Telepon\",\n    userLevel: \"Level Pengguna\",\n    query: \"Cari\",\n    reset: \"Reset\",\n    serialNumber: \"Nomor Urut\",\n    avatar: \"Avatar\",\n    tiktokAccount: \"Panggilan TikTok\",\n    tiktokId: \"ID TikTok\",\n    whatsApp: \"Nomor WhatsApp\",\n    registerTime: \"Waktu Pendaftaran\",\n    lastLoginTime: \"Waktu Login Terakhir\",\n    orderCount: \"Jumlah Pesanan\",\n    orderFinishCount: \"Jumlah Pesanan Selesai\",\n    isAgent: \"Apakah Agen\",\n    isPartner: \"Apakah Mitra\",\n    userLevelLabel: \"Level Pengguna\",\n    inviter: \"Pengundang\",\n    userTags: \"Tag Pengguna\"\n  },\n  levelUpgrade: {\n    title: \"Manajemen Pesanan Peningkatan Level\",\n    orderNo: \"Nomor Pesanan\",\n    userId: \"ID Pengguna\",\n    upgradeInfo: \"Info Peningkatan\",\n    upgradeFee: \"Biaya Peningkatan\",\n    paymentMethod: \"Metode Pembayaran\",\n    orderStatus: \"Status Pesanan\",\n    createTime: \"Waktu Dibuat\",\n    payTime: \"Waktu Pembayaran\",\n    operation: \"Operasi\",\n    enterOrderNo: \"Masukkan nomor pesanan\",\n    selectStatus: \"Pilih status\",\n    pending: \"Menunggu Pembayaran\",\n    paid: \"Sudah Dibayar\",\n    cancelled: \"Dibatalkan\",\n    refunded: \"Dikembalikan\",\n    cancelOrder: \"Batalkan Pesanan\",\n    viewDetail: \"Lihat Detail\",\n    orderDetail: \"Detail Pesanan\",\n    fromLevel: \"Level Asal\",\n    toLevel: \"Level Tujuan\",\n    remark: \"Catatan\",\n    noRemark: \"Tidak ada\",\n    unpaid: \"Belum Dibayar\",\n    confirmCancel: \"Apakah Anda yakin ingin membatalkan pesanan ini?\",\n    cancelSuccess: \"Pesanan berhasil dibatalkan\",\n    cancelFailed: \"Gagal membatalkan pesanan\",\n    getListFailed: \"Gagal mendapatkan daftar pesanan\",\n    balancePayment: \"Pembayaran Saldo\",\n    unknownLevel: \"Level Tidak Dikenal\",\n    unknownStatus: \"Status Tidak Dikenal\",\n    changeWarning: \"Jangan sering mengubah untuk menghindari kebingungan perhitungan!\",\n    deductExperience: \"Kurangi Pengalaman\",\n    levelNames: {\n      1: \"Pengguna Biasa\",\n      2: \"Pengguna Silver\",\n      3: \"Pengguna Emas\",\n      4: \"Pengguna Berlian\",\n      5: \"Pengguna Raja\",\n      6: \"Pengguna Master\"\n    }\n  },\n  grade: {\n    title: \"Level Pengguna\",\n    levelName: \"Nama Level\",\n    experience: \"Pengalaman\",\n    discount: \"Diskon\",\n    commissionRate: \"Tingkat Komisi\",\n    upgradeType: \"Jenis Peningkatan\",\n    upgradeFee: \"Biaya Peningkatan\",\n    availableStatus: \"Status Ketersediaan\",\n    status: \"Status\",\n    operation: \"Operasi\",\n    available: \"Tersedia\",\n    unavailable: \"Tidak Tersedia\",\n    free: \"Gratis\",\n    addUserLevel: \"Tambah Level Pengguna\",\n    levelIcon: \"Ikon Level\",\n    enable: \"Aktifkan\",\n    disable: \"Nonaktifkan\",\n    edit: \"Edit\",\n    delete: \"Hapus\",\n    deleteConfirm: \"Yakin ingin menghapus? Ini akan menghapus data level pengguna terkait, harap berhati-hati!\",\n    deleteSuccess: \"Berhasil dihapus\",\n    updateSuccess: \"Berhasil diperbarui\",\n    hideConfirm: \"Operasi ini akan menyembunyikan level pengguna terkait, harap berhati-hati\",\n    userTypes: {\n      wechat: \"Pengguna WeChat\",\n      routine: \"Pengguna Mini Program\",\n      h5: \"Pengguna H5\"\n    },\n    upgradeTypes: {\n      0: \"Daftar Langsung\",\n      1: \"Pembelian Berbayar\",\n      2: \"Aplikasi Offline\",\n      3: \"Kemitraan Saluran\"\n    },\n    form: {\n      dialogTitle: \"Level Pengguna\",\n      levelNameLabel: \"Nama Level\",\n      levelNamePlaceholder: \"Masukkan nama level\",\n      gradeLabel: \"Grade\",\n      gradePlaceholder: \"Masukkan grade\",\n      discountLabel: \"Diskon (%)\",\n      discountPlaceholder: \"Masukkan diskon\",\n      experienceLabel: \"Pengalaman\",\n      experiencePlaceholder: \"Masukkan pengalaman\",\n      iconLabel: \"Ikon\",\n      cancel: \"Batal\",\n      confirm: \"Konfirmasi\",\n      editSuccess: \"Edit berhasil\",\n      addSuccess: \"Tambah berhasil\",\n      validation: {\n        levelNameRequired: \"Masukkan nama level\",\n        gradeRequired: \"Masukkan grade\",\n        gradeNumber: \"Grade harus berupa angka\",\n        discountRequired: \"Masukkan diskon\",\n        experienceRequired: \"Masukkan pengalaman\",\n        experienceNumber: \"Pengalaman harus berupa angka\",\n        iconRequired: \"Unggah ikon\",\n        imageRequired: \"Unggah latar belakang pengguna\"\n      }\n    }\n  }\n}), \"financial\", {\n  detail: _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({\n    title: \"Rincian Keuangan\",\n    purchaseDetail: \"Rincian Pembelian Anggota\",\n    tradeDetail: \"Rincian Transaksi\",\n    rechargeType: \"Nama Produk\",\n    transactionTime: \"Waktu Transaksi\",\n    paymentMethod: \"Metode Pembayaran\",\n    electronicWallet: \"Dompet Elektronik\",\n    bankName: \"Nama Bank\",\n    serialNumber: \"Nomor Urut\",\n    paymentTime: \"Waktu Pembayaran\",\n    paymentNo: \"Nomor Pembayaran\",\n    actualPaymentAmount: \"Jumlah Pembayaran Aktual\",\n    institutionNumber: \"Nomor Institusi\",\n    paymentAccount: \"Akun Pembayaran\",\n    mobile: \"Nomor Telepon\",\n    payee: \"Penerima\",\n    payeeAccount: \"Akun Penerima\",\n    tradeNo: \"Nomor Transaksi\",\n    tradeType: \"Tipe Transaksi\",\n    tradeAmount: \"Jumlah Transaksi (Rp)\",\n    userNickname: \"Nama Panggilan Pengguna\",\n    tikTokAccount: \"Akun TikTok\",\n    whatsApp: \"WhatsApp\",\n    channel: \"Saluran\",\n    orderNo: \"Nomor Pesanan\",\n    bankTransfer: \"Transfer Bank\"\n  }, \"electronicWallet\", \"Dompet Elektronik\"), \"agentFee\", \"Biaya Agen\"), \"partnerFee\", \"Biaya Mitra\"), \"exportExcel\", \"Ekspor ke Excel\"), \"ShopeePay\", \"ShopeePay\"), \"DANA\", \"DANA\"), \"OVO\", \"OVO\"), \"Gopay\", \"Gopay\"),\n  request: (_request = {\n    walletWithdrawal: \"Penarikan Dompet Elektronik\",\n    bankWithdrawal: \"Penarikan Bank\",\n    applicant: \"Pemohon\",\n    applicationTime: \"Waktu Pengajuan\",\n    electronicWallet: \"Dompet Elektronik\",\n    bankName: \"Nama Bank\",\n    serialNumber: \"Nomor Urut\",\n    applicationId: \"ID Pengajuan\",\n    applicantName: \"Pemohon\",\n    withdrawalAmount: \"Jumlah Penarikan\",\n    serviceFee: \"Biaya Layanan\",\n    actualAmount: \"Jumlah yang Diterima\"\n  }, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_request, \"applicationTime\", \"Waktu Pengajuan\"), \"walletCode\", \"Dompet Elektronik\"), \"walletAccount\", \"Akun\"), \"bankCardNumber\", \"Nomor Kartu Bank\"), \"name\", \"Nama\"), \"phoneNumber\", \"Nomor Telepon\"), \"action\", \"Aksi\"), \"transferComplete\", \"Transfer Selesai\"), \"attachment\", \"Lampiran\"), \"remark\", \"Catatan\"), _defineProperty(_defineProperty(_defineProperty(_request, \"confirm\", \"Konfirmasi\"), \"cancel\", \"Batal\"), \"exportExcel\", \"Ekspor ke Excel\")),\n  history: (_history = {\n    walletWithdrawal: \"Penarikan Dompet Elektronik\",\n    bankWithdrawal: \"Penarikan Bank\",\n    applicant: \"Pemohon\",\n    applicationTime: \"Waktu Pengajuan\",\n    electronicWallet: \"Dompet Elektronik\",\n    bankName: \"Nama Bank\",\n    serialNumber: \"Nomor Urut\",\n    applicationId: \"ID Pengajuan\",\n    applicantName: \"Pemohon\",\n    withdrawalAmount: \"Jumlah Penarikan\",\n    serviceFee: \"Biaya Layanan\",\n    actualAmount: \"Jumlah yang Diterima\"\n  }, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_history, \"applicationTime\", \"Waktu Pengajuan\"), \"walletCode\", \"Dompet Elektronik\"), \"walletAccount\", \"Akun\"), \"bankCardNumber\", \"Nomor Kartu Bank\"), \"name\", \"Nama\"), \"phoneNumber\", \"Nomor Telepon\"), \"transferTime\", \"Waktu Transfer\"), \"transferResult\", \"Hasil Transfer\"), \"remark\", \"Catatan\"), \"attachment\", \"Lampiran\"), _defineProperty(_defineProperty(_defineProperty(_history, \"operator\", \"Operator\"), \"exportExcel\", \"Ekspor ke Excel\"), \"status\", \"Status Penarikan\"))\n}), \"parameter\", {\n  rewardRules: {\n    title: \"Pengaturan Aturan Hadiah\",\n    rewardTemplateName: \"Nama Template\",\n    rewardTemplateId: \"ID Template Aturan Hadiah\",\n    directInviteReward: \"Hadiah Langsung per Orang\",\n    secondLevelInviteReward: \"Hadiah Tidak Langsung (Level 2) per Orang (Rp)\",\n    thirdLevelInviteReward: \"Hadiah Tidak Langsung (Level 3) per Orang (Rp)\",\n    goldRewardPer10: \"Hadiah Emas per 10 Orang (Rp)\",\n    diamondRewardPer10: \"Hadiah Berlian per 10 Orang (Rp)\",\n    operation: \"Operasi\",\n    edit: \"Ubah\",\n    editTitle: \"Ubah Pengaturan Aturan Hadiah\",\n    directAgentLabel: \"Undangan langsung adalah agen\",\n    directPartnerLabel: \"Undangan langsung adalah mitra\",\n    indirectAgent2LevelLabel: \"Undangan tidak langsung (level 2 adalah agen) setiap orang bisa dapat (Rp)\",\n    indirectPartner2LevelLabel: \"Undangan tidak langsung (level 2 adalah mitra) setiap orang bisa dapat (Rp)\",\n    indirectAgent3LevelLabel: \"Undangan tidak langsung (level 3 adalah agen) setiap orang bisa dapat (Rp)\",\n    indirectPartner3LevelLabel: \"Undangan tidak langsung (level 3 adalah mitra) setiap orang bisa dapat (Rp)\"\n  },\n  withdrawalFee: {\n    title: \"Pengaturan Biaya Penarikan\",\n    feeTemplateId: \"ID Template Biaya\",\n    minWithdrawAmount: \"Jumlah Penarikan Minimum (Rp)\",\n    maxWithdrawAmount: \"Jumlah Penarikan Maksimum (Rp)\",\n    withdrawFeeRate: \"Tarif Biaya Penarikan (%)\",\n    operation: \"Operasi\",\n    edit: \"Ubah\",\n    addTitle: \"Tambah Aturan Biaya\",\n    editTitle: \"Ubah Aturan Biaya\",\n    placeholder: {\n      couponId: \"Masukkan ID kupon\",\n      minWithdrawAmount: \"Masukkan jumlah penarikan minimum\",\n      maxWithdrawAmount: \"Masukkan jumlah penarikan maksimum\",\n      withdrawFeeRate: \"Masukkan tarif biaya penarikan\"\n    }\n  },\n  membershipFee: {\n    title: \"Pengaturan Biaya Peningkatan Keanggotaan\",\n    feeTemplateId: \"ID Template Biaya\",\n    agentFee: \"Biaya Agen (Rp)\",\n    partnerFee: \"Biaya Mitra (Rp)\",\n    operation: \"Operasi\",\n    edit: \"Ubah\",\n    addTitle: \"Tambah Biaya Peningkatan Keanggotaan\",\n    editTitle: \"Ubah Biaya Peningkatan Keanggotaan\",\n    placeholder: {\n      agentFee: \"Masukkan biaya agen\",\n      partnerFee: \"Masukkan biaya mitra\"\n    }\n  },\n  shoppingCashbackRules: {\n    directCashbackRate: \"Tingkat Cashback Langsung (%)\",\n    secondLevelCashbackRate: \"Tingkat Cashback Level 2 (%)\",\n    thirdLevelCashbackRate: \"Tingkat Cashback Level 3 (%)\",\n    normalUserRule: \"Aturan Cashback Pengguna Normal\",\n    agentTeamRule: \"Aturan Cashback Tim Agen\",\n    partnerTeamRule: \"Aturan Cashback Tim Mitra\"\n  },\n  referralRewardConfig: {\n    title: \"Konfigurasi Hadiah Rujukan\",\n    rewardTemplateId: \"ID Template Aturan Hadiah\",\n    rewardTemplateName: \"Nama Template\",\n    referralCount: \"Jumlah Rujukan\",\n    firstOrderCount: \"Jumlah Pesanan Pertama\",\n    rewardAmount: \"Jumlah Hadiah (Rp)\",\n    rewardRuleZh: \"Aturan Hadiah (Cina)\",\n    rewardRuleEn: \"Aturan Hadiah (Inggris)\",\n    rewardRuleId: \"Aturan Hadiah (Indonesia)\",\n    operation: \"Operasi\",\n    edit: \"Ubah\",\n    editTitle: \"Ubah Konfigurasi Hadiah Rujukan\",\n    basicConfig: \"Konfigurasi Dasar\",\n    validation: {\n      referralCountMin: \"Jumlah rujukan harus lebih besar atau sama dengan 0\",\n      firstOrderCountMin: \"Jumlah pesanan pertama harus lebih besar atau sama dengan 0\",\n      firstOrderCountMax: \"Jumlah pesanan pertama harus kurang dari jumlah rujukan\",\n      rewardAmountMin: \"Jumlah hadiah harus lebih besar atau sama dengan 0\"\n    }\n  }\n}), \"admin\", {\n  system: {\n    role: {\n      roleName: \"Nama Peran\",\n      roleId: \"ID Peran\",\n      status: \"Status\",\n      createTime: \"Waktu Dibuat\",\n      updateTime: \"Waktu Diperbarui\",\n      operation: \"Operasi\",\n      addRole: \"Tambah Peran\",\n      editRole: \"Ubah Peran\",\n      deleteRole: \"Hapus Peran\",\n      confirmDelete: \"Yakin hapus data ini?\",\n      deleteSuccess: \"Data berhasil dihapus\",\n      createIdentity: \"Buat Identitas\",\n      editIdentity: \"Ubah Identitas\",\n      roleForm: {\n        roleNameLabel: \"Nama Peran\",\n        roleNamePlaceholder: \"Nama Identitas\",\n        statusLabel: \"Status\",\n        menuPermissions: \"Izin Menu\",\n        expandCollapse: \"Ekspansi/Kolaps\",\n        selectAll: \"Pilih Semua/Tidak\",\n        parentChildLink: \"Kaitan Induk-Anak\",\n        confirm: \"Konfirmasi\",\n        update: \"Perbarui\",\n        cancel: \"Batal\"\n      }\n    },\n    admin: (_admin = {\n      role: \"Peran\",\n      status: \"Status\",\n      realName: \"Nama atau Akun\",\n      id: \"ID\",\n      account: \"Akun\",\n      phone: \"Nomor Telepon\",\n      lastTime: \"Waktu Login Terakhir\",\n      lastIp: \"IP Login Terakhir\",\n      isSms: \"Terima SMS\",\n      isDel: \"Tanda Hapus\",\n      operation: \"Operasi\",\n      addAdmin: \"Tambah Admin\",\n      edit: \"Ubah\",\n      delete: \"Hapus\",\n      createIdentity: \"Buat Identitas\",\n      editIdentity: \"Ubah Identitas\",\n      pleaseAddPhone: \"Silakan tambahkan nomor telepon untuk admin terlebih dahulu!\",\n      confirmDelete: \"Yakin hapus data ini?\",\n      deleteSuccess: \"Data berhasil dihapus\"\n    }, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_admin, \"account\", \"Akun\"), \"pwd\", \"Kata Sandi\"), \"repwd\", \"Konfirmasi Kata Sandi\"), \"realName\", \"Nama\"), \"roles\", \"Peran\"), \"phone\", \"Nomor Telepon\"), \"pleaseAddPhone\", \"Silakan tambahkan nomor telepon untuk admin terlebih dahulu!\"), \"validatePhone\", {\n      required: \"Silakan masukkan nomor telepon\",\n      formatError: \"Format nomor telepon tidak benar!\"\n    }), \"validatePass\", {\n      required: \"Silakan masukkan ulang kata sandi\",\n      notMatch: \"Kata sandi tidak cocok!\"\n    }), \"message\", {\n      createSuccess: \"Admin berhasil dibuat\",\n      updateSuccess: \"Admin berhasil diperbarui\"\n    }), _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_admin, \"validateAccount\", {\n      required: \"Silakan masukkan akun admin\"\n    }), \"validatePassword\", {\n      required: \"Silakan masukkan kata sandi admin\"\n    }), \"validateConfirmPassword\", {\n      required: \"Silakan konfirmasi kata sandi\"\n    }), \"validateRealName\", {\n      required: \"Silakan masukkan nama admin\"\n    }), \"validateRoles\", {\n      required: \"Silakan pilih peran admin\"\n    }), \"validatePassword\", {\n      required: \"Silakan masukkan kata sandi admin\",\n      lengthError: \"Panjang kata sandi harus 6-20 karakter\"\n    }), \"validateConfirmPassword\", {\n      required: \"Silakan konfirmasi kata sandi\"\n    }), \"validatePass\", {\n      notMatch: \"Kata sandi tidak cocok\"\n    }))\n  }\n}), \"permissionRules\", {\n  menuName: \"Nama Menu\",\n  status: \"Status\",\n  select: \"Silakan Pilih\",\n  add: \"Tambah\",\n  expandCollapse: \"Ekspansi/Kolaps\",\n  actions: {\n    edit: \"Ubah\",\n    add: \"Tambah\",\n    delete: \"Hapus\"\n  },\n  table: {\n    menuName: \"Nama Menu\",\n    icon: \"Ikon\",\n    sort: \"Urutan\",\n    perm: \"Karakter Hak\",\n    component: \"Jalur Komponen\",\n    status: \"Status\",\n    createTime: \"Waktu Buat\",\n    type: \"Jenis\"\n  },\n  menuType: {\n    directory: \"Direktori\",\n    menu: \"Menu\",\n    button: \"Tombol\"\n  },\n  form: {\n    parentMenu: \"Menu Induk\",\n    menuType: \"Jenis Menu\",\n    menuIcon: \"Ikon Menu\",\n    menuName: \"Nama Menu\",\n    sort: \"Urutan Tampil\",\n    component: \"Jalur Komponen\",\n    componentTip: \"Jalur komponen, contoh: `system/user/index`, default di folder `views`\",\n    perm: \"Karakter Hak\",\n    permTip: 'Karakter hak yang didefinisikan di controller, contoh: @PreAuthorize(`@ss.hasPermi(\"system:user:list\")`)',\n    showStatus: \"Status Tampil\",\n    showStatusTip: \"Jika disembunyikan, menu tidak akan muncul di sidebar namun tetap bisa diakses\",\n    enterMenuName: \"Masukkan nama menu\",\n    enterComponent: \"Masukkan jalur komponen\",\n    enterPerm: \"Masukkan karakter hak\",\n    selectIcon: \"Silakan pilih ikon menu\",\n    selectParentMenu: \"Pilih menu induk\",\n    sortRequired: \"Urutan tampil tidak boleh kosong\"\n  }\n}), \"affiliateProducts\", (_affiliateProducts = {\n  title: \"Produk Afiliasi\",\n  keywords: \"Kata Kunci:\",\n  keywordsPlaceholder: \"Masukkan kata kunci produk\",\n  priceRange: \"Rentang Harga:\",\n  minPrice: \"Harga Minimum\",\n  maxPrice: \"Harga Maksimum\",\n  commissionRange: \"Rentang Tingkat Komisi:\",\n  minCommission: \"Tingkat Komisi Minimum (%)\",\n  maxCommission: \"Tingkat Komisi Maksimum (%)\",\n  sort: \"Urutkan:\",\n  sortCommissionRate: \"Tingkat Komisi\",\n  sortCommission: \"Jumlah Komisi\",\n  sortPrice: \"Harga Produk\",\n  sortSales: \"Penjualan\",\n  sortDesc: \"Menurun\",\n  sortAsc: \"Menaik\",\n  query: \"Cari\",\n  reset: \"Reset\",\n  refresh: \"Segarkan\",\n  listTitle: \"Daftar Produk Afiliasi\",\n  batchImport: \"Impor Massal\",\n  batchImporting: \"Mengimpor Massal...\",\n  batchDelete: \"Hapus Massal\",\n  batchDeleting: \"Menghapus Massal...\",\n  emptyTip: \"Klik tombol cari untuk mulai mencari produk\",\n  serialNumber: \"Nomor Urut\",\n  productImage: \"Gambar Produk\",\n  productTitle: \"Judul Produk\",\n  shop: \"Toko\",\n  originalPrice: \"Harga Asli\",\n  salesPrice: \"Harga Jual\",\n  commissionRate: \"Tingkat Komisi\",\n  commissionAmount: \"Jumlah Komisi\",\n  unitsSold: \"Unit Terjual\",\n  inventoryStatus: \"Status Inventori\",\n  hasInventory: \"Tersedia\",\n  noInventory: \"Habis\",\n  saleRegion: \"Wilayah Penjualan\",\n  importStatus: \"Status Impor\",\n  imported: \"Telah Diimpor\",\n  notImported: \"Belum Diimpor\",\n  action: \"Aksi\",\n  import: \"Impor Produk\",\n  importing: \"Mengimpor...\"\n}, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_affiliateProducts, \"imported\", \"Telah Diimpor\"), \"delete\", \"Hapus\"), \"deleting\", \"Menghapus...\"), \"prevPage\", \"Halaman Sebelumnya\"), \"nextPage\", \"Halaman Berikutnya\"), \"pageSize\", \"Item per halaman:\"), \"totalCount\", \"Total {count} produk\"), \"importSingle\", \"Impor Produk Tunggal\"), \"importBatch\", \"Impor Produk Massal\"), \"selectedCount\", \"Produk Terpilih:\"), _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_affiliateProducts, \"brandAutoDetect\", \"Informasi merek akan otomatis diidentifikasi dari data produk TikTok, tidak perlu pemilihan manual\"), \"confirmImport\", \"Konfirmasi Impor\"), \"cancel\", \"Batal\"), \"deleteConfirm\", \"Apakah Anda yakin ingin menghapus produk ini?\"), \"batchDeleteConfirm\", \"Apakah Anda yakin ingin menghapus {count} produk secara massal?\"), \"deleteSuccess\", \"Berhasil dihapus\"), \"batchDeleteSuccess\", \"Berhasil menghapus secara massal\"), \"importSuccess\", \"Produk berhasil diimpor!\"), \"batchImportSuccess\", \"Impor massal selesai! Berhasil mengimpor {count} produk\"), \"importExists\", \"Produk sudah ada, tidak perlu diimpor lagi\"), _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_affiliateProducts, \"batchImportExists\", \"Semua produk sudah ada, tidak perlu diimpor lagi\"), \"batchImportPartial\", \"Impor massal sebagian berhasil! Berhasil: {success}, Gagal: {failed}, Dilewati: {skipped}\"), \"batchImportMixed\", \"Impor massal selesai! Berhasil: {success}, Dilewati (sudah ada): {skipped}\"), \"importFailed\", \"Impor produk gagal! Alasan: {reason}\"), \"batchImportFailed\", \"Impor massal gagal! Gagal: {failed}, Dilewati: {skipped}\"), \"selectFirst\", \"Silakan pilih produk yang akan diimpor terlebih dahulu\"), \"selectDeleteFirst\", \"Silakan pilih produk yang akan dihapus terlebih dahulu\"), \"searchFirst\", \"Silakan klik tombol cari terlebih dahulu\"), \"noResults\", \"Tidak ditemukan produk yang sesuai kriteria\"), \"commissionRangeError\", \"Tingkat komisi harus ≥ 1000 atau sama dengan 0\"), _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_affiliateProducts, \"commissionInvalidNumber\", \"Silakan masukkan angka yang valid\"), \"pleaseFixErrors\", \"Silakan perbaiki kesalahan input sebelum mencari\"), \"addKeywordPlaceholder\", \"Masukkan kata kunci dan tekan Enter untuk menambah\"), \"keywordTooLong\", \"Panjang kata kunci tidak boleh melebihi 255 karakter\"), \"keywordDuplicate\", \"Kata kunci sudah ada, jangan menambahkan duplikat\"), \"keywordLimitExceeded\", \"Jumlah kata kunci tidak boleh melebihi 20\"), \"selectedKeywords\", \"Kata Kunci Terpilih:\"), \"clearAll\", \"Hapus Semua\"), \"clearAllKeywords\", \"Hapus Semua Kata Kunci\"), \"confirmClearAllKeywords\", \"Apakah Anda yakin ingin menghapus semua kata kunci yang dipilih?\"), _defineProperty(_affiliateProducts, \"keywordsClearedSuccess\", \"Semua kata kunci telah dihapus\"))), \"chainTransferRecord\", {\n  title: \"Rekam Beralih Rantai\",\n  keyword: \"Kata Kunci\",\n  brandName: \"Nama Merek\",\n  query: \"Cari\",\n  reset: \"Reset\",\n  exportExcel: \"Ekspor ke Excel\",\n  serialNumber: \"Nomor Urut\",\n  nickname: \"Nama Panggilan\",\n  tiktokId: \"ID TikTok\",\n  originalLink: \"Tautan Asli\",\n  rebateLink: \"Tautan Rebate Setelah Beralih Rantai\",\n  operationTime: \"Waktu Operasi\",\n  linkSource: \"Sumber Tautan\",\n  productId: \"ID Produk\",\n  productName: \"Nama Produk\",\n  productPrice: \"Harga Produk\",\n  productCashbackRate: \"Tingkat Cashback Produk\",\n  userCashbackRate: \"Tingkat Cashback Pengguna\",\n  enterProductName: \"Silakan masukkan nama produk\"\n}), \"message\", {\n  hello: \"Halo\",\n  userNotice: \"Pemberitahuan Pengguna\",\n  userDetails: {\n    balance: \"Saldo\",\n    allOrderCount: \"Total Pesanan\",\n    allConsumeCount: \"Total Konsumsi\",\n    integralCount: \"Poin\",\n    mothOrderCount: \"Pesanan Bulan Ini\",\n    mothConsumeCount: \"Konsumsi Bulan Ini\",\n    consumeRecord: \"Catatan Konsumsi\",\n    integralDetail: \"Detail Poin\",\n    signInRecord: \"Catatan Masuk\",\n    coupons: \"Kupon Dimiliki\",\n    balanceChange: \"Perubahan Saldo\",\n    friendRelation: \"Relasi Teman\",\n    sourceOrPurpose: \"Sumber/Tujuan\",\n    integralChange: \"Perubahan Poin\",\n    balanceAfterChange: \"Poin Setelah Perubahan\",\n    date: \"Tanggal\",\n    remark: \"Catatan\",\n    orderId: \"ID Pesanan\",\n    receiver: \"Penerima\",\n    goodsNum: \"Jumlah Barang\",\n    goodsTotalPrice: \"Total Harga Barang\",\n    payPrice: \"Jumlah Dibayar\",\n    payTime: \"Waktu Transaksi\",\n    action: \"Aksi\",\n    getIntegral: \"Poin Didapat\",\n    signTime: \"Waktu Masuk\",\n    couponName: \"Nama Kupon\",\n    faceValue: \"Nilai Kupon\",\n    validity: \"Masa Berlaku\",\n    minPrice: \"Konsumsi Minimum\",\n    exchangeTime: \"Waktu Penukaran\",\n    changeAmount: \"Jumlah Perubahan\",\n    afterChange: \"Setelah Perubahan\",\n    type: \"Tipe\",\n    createTime: \"Waktu Buat\",\n    id: \"ID\",\n    nickname: \"Nama Panggilan\",\n    level: \"Level\",\n    joinTime: \"Waktu Bergabung\"\n  }\n}));", null]}
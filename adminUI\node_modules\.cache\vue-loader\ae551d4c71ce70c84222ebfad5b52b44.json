{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\operations\\chainTransferRecord\\index.vue?vue&type=template&id=132279b7&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\operations\\chainTransferRecord\\index.vue", "mtime": 1754377406953}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754052682065}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"divBox relative\" },\n    [\n      _c(\n        \"el-card\",\n        { staticClass: \"box-card\" },\n        [\n          _c(\n            \"div\",\n            { staticClass: \"container mt-1\" },\n            [\n              _c(\n                \"el-form\",\n                {\n                  attrs: { inline: \"\", size: \"small\" },\n                  model: {\n                    value: _vm.searchFrom,\n                    callback: function ($$v) {\n                      _vm.searchFrom = $$v\n                    },\n                    expression: \"searchFrom\",\n                  },\n                },\n                [\n                  _c(\n                    \"el-form-item\",\n                    {\n                      attrs: {\n                        label: _vm.$t(\"chainTransferRecord.keyword\") + \"：\",\n                      },\n                    },\n                    [\n                      _c(\"el-input\", {\n                        attrs: {\n                          placeholder: _vm.$t(\n                            \"chainTransferRecord.enterProductName\"\n                          ),\n                          clearable: \"\",\n                        },\n                        model: {\n                          value: _vm.searchFrom.keyword,\n                          callback: function ($$v) {\n                            _vm.$set(_vm.searchFrom, \"keyword\", $$v)\n                          },\n                          expression: \"searchFrom.keyword\",\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-form-item\",\n                    {\n                      attrs: {\n                        label: _vm.$t(\"chainTransferRecord.brandName\") + \"：\",\n                      },\n                    },\n                    [\n                      _c(\n                        \"el-select\",\n                        {\n                          attrs: {\n                            placeholder: _vm.$t(\"common.all\"),\n                            clearable: \"\",\n                          },\n                          model: {\n                            value: _vm.searchFrom.brandCode,\n                            callback: function ($$v) {\n                              _vm.$set(_vm.searchFrom, \"brandCode\", $$v)\n                            },\n                            expression: \"searchFrom.brandCode\",\n                          },\n                        },\n                        _vm._l(_vm.brandList, function (item) {\n                          return _c(\"el-option\", {\n                            key: item.code,\n                            attrs: { label: item.name, value: item.code },\n                          })\n                        }),\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-button\",\n            {\n              staticClass: \"mr10\",\n              attrs: { size: \"small\", type: \"primary\" },\n              on: {\n                click: function ($event) {\n                  return _vm.getList(1)\n                },\n              },\n            },\n            [\n              _vm._v(\n                \"\\n      \" +\n                  _vm._s(_vm.$t(\"chainTransferRecord.query\")) +\n                  \"\\n    \"\n              ),\n            ]\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-button\",\n            {\n              staticClass: \"mr10\",\n              attrs: { size: \"small\", type: \"\" },\n              on: { click: _vm.resetForm },\n            },\n            [\n              _vm._v(\n                \"\\n      \" +\n                  _vm._s(_vm.$t(\"chainTransferRecord.reset\")) +\n                  \"\\n    \"\n              ),\n            ]\n          ),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-card\",\n        { staticClass: \"box-card\", staticStyle: { \"margin-top\": \"12px\" } },\n        [\n          _c(\n            \"div\",\n            {\n              staticClass: \"clearfix\",\n              attrs: { slot: \"header\" },\n              slot: \"header\",\n            },\n            [\n              _c(\n                \"el-button\",\n                {\n                  directives: [\n                    {\n                      name: \"hasPermi\",\n                      rawName: \"v-hasPermi\",\n                      value: [\"admin:financialCenter:request:upload\"],\n                      expression: \"['admin:financialCenter:request:upload']\",\n                    },\n                  ],\n                  attrs: { type: \"primary\", size: \"small\" },\n                },\n                [\n                  _vm._v(\n                    \"\\n        \" +\n                      _vm._s(_vm.$t(\"chainTransferRecord.exportExcel\")) +\n                      \"\\n      \"\n                  ),\n                ]\n              ),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-table\",\n            {\n              directives: [\n                {\n                  name: \"loading\",\n                  rawName: \"v-loading\",\n                  value: _vm.loading,\n                  expression: \"loading\",\n                },\n              ],\n              attrs: {\n                data: _vm.tableData,\n                size: \"small\",\n                \"header-cell-style\": { fontWeight: \"bold\" },\n              },\n            },\n            [\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"chainTransferRecord.serialNumber\"),\n                  type: \"index\",\n                  width: \"110\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: _vm.$t(\"chainTransferRecord.nickname\") },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"span\", [\n                          _vm._v(\n                            _vm._s(_vm._f(\"filterEmpty\")(scope.row.userAccount))\n                          ),\n                        ]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: _vm.$t(\"chainTransferRecord.tiktokId\") },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"span\", [\n                          _vm._v(\n                            _vm._s(_vm._f(\"filterEmpty\")(scope.row.tiktokUid))\n                          ),\n                        ]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: _vm.$t(\"chainTransferRecord.originalLink\") },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"span\", [\n                          _vm._v(\n                            _vm._s(_vm._f(\"filterEmpty\")(scope.row.originUrl))\n                          ),\n                        ]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: _vm.$t(\"chainTransferRecord.rebateLink\") },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"span\", [\n                          _vm._v(\n                            _vm._s(_vm._f(\"filterEmpty\")(scope.row.shareUrl))\n                          ),\n                        ]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: _vm.$t(\"chainTransferRecord.operationTime\") },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"span\", [\n                          _vm._v(\n                            _vm._s(_vm._f(\"filterEmpty\")(scope.row.operateTime))\n                          ),\n                        ]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: _vm.$t(\"chainTransferRecord.linkSource\") },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"span\", [\n                          _vm._v(\n                            _vm._s(_vm._f(\"filterEmpty\")(scope.row.channel))\n                          ),\n                        ]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: _vm.$t(\"chainTransferRecord.productId\") },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"span\", [\n                          _vm._v(\n                            _vm._s(_vm._f(\"filterEmpty\")(scope.row.productId))\n                          ),\n                        ]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: _vm.$t(\"chainTransferRecord.productName\") },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"span\", [\n                          _vm._v(\n                            _vm._s(_vm._f(\"filterEmpty\")(scope.row.productName))\n                          ),\n                        ]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: _vm.$t(\"chainTransferRecord.productPrice\") },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"span\", [\n                          _vm._v(\n                            _vm._s(\n                              _vm._f(\"filterEmpty\")(\n                                _vm.formatAmount(scope.row.productPrice)\n                              )\n                            )\n                          ),\n                        ]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"chainTransferRecord.productCashbackRate\"),\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _vm._v(\n                          _vm._s(_vm.formatRate(scope.row.productCashbackRate))\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\"el-pagination\", {\n            staticClass: \"mt20\",\n            attrs: {\n              \"current-page\": _vm.searchFrom.page,\n              \"page-sizes\": [20, 40, 60, 100],\n              \"page-size\": _vm.searchFrom.limit,\n              layout: \"total, sizes, prev, pager, next, jumper\",\n              total: _vm.searchFrom.total,\n            },\n            on: {\n              \"size-change\": _vm.sizeChange,\n              \"current-change\": _vm.pageChange,\n            },\n          }),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}